<?php
/**
 * صفحة تشخيص مشاكل التحميل
 * Download Issues Debugging Page
 */

require_once 'config.php';

// فحص المجلدات والملفات
$checks = [];

// فحص مجلد downloads
$downloads_dir = __DIR__ . '/downloads';
$checks['downloads_dir_exists'] = is_dir($downloads_dir);
$checks['downloads_dir_writable'] = is_dir($downloads_dir) && is_writable($downloads_dir);

// فحص الملفات الموجودة
$apk_files = [];
if (is_dir($downloads_dir)) {
    $files = scandir($downloads_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'apk') {
            $filepath = $downloads_dir . '/' . $file;
            $apk_files[] = [
                'name' => $file,
                'size' => filesize($filepath),
                'date' => filemtime($filepath),
                'readable' => is_readable($filepath)
            ];
        }
    }
}

// فحص إعدادات PHP
$php_settings = [
    'file_uploads' => ini_get('file_uploads'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit')
];

// فحص المكتبات
$extensions = [
    'gd' => extension_loaded('gd'),
    'zip' => extension_loaded('zip'),
    'json' => extension_loaded('json')
];

// إذا كان هناك طلب لتحميل ملف تجريبي
if (isset($_GET['test_download'])) {
    $test_content = "ملف اختبار التحميل\nTest Download File\nالوقت: " . date('Y-m-d H:i:s');
    
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="test_download.txt"');
    header('Content-Length: ' . strlen($test_content));
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    
    echo $test_content;
    exit;
}

function formatBytes($size) {
    $units = ['B', 'KB', 'MB', 'GB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, 2) . ' ' . $units[$i];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل التحميل</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .debug-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-ok {
            color: #4caf50;
        }
        
        .status-error {
            color: #f44336;
        }
        
        .file-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .test-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-btn:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <header>
            <h1><i class="fas fa-bug"></i> تشخيص مشاكل التحميل</h1>
            <p>فحص شامل لمشاكل التحميل وحلولها</p>
        </header>

        <div class="debug-section">
            <h2><i class="fas fa-folder"></i> فحص المجلدات</h2>
            
            <div class="status-item">
                <span>مجلد downloads موجود</span>
                <span class="<?= $checks['downloads_dir_exists'] ? 'status-ok' : 'status-error' ?>">
                    <i class="fas fa-<?= $checks['downloads_dir_exists'] ? 'check' : 'times' ?>"></i>
                    <?= $checks['downloads_dir_exists'] ? 'نعم' : 'لا' ?>
                </span>
            </div>
            
            <div class="status-item">
                <span>مجلد downloads قابل للكتابة</span>
                <span class="<?= $checks['downloads_dir_writable'] ? 'status-ok' : 'status-error' ?>">
                    <i class="fas fa-<?= $checks['downloads_dir_writable'] ? 'check' : 'times' ?>"></i>
                    <?= $checks['downloads_dir_writable'] ? 'نعم' : 'لا' ?>
                </span>
            </div>
            
            <div class="status-item">
                <span>مسار مجلد downloads</span>
                <span style="font-family: monospace; font-size: 0.9rem;"><?= $downloads_dir ?></span>
            </div>
        </div>

        <div class="debug-section">
            <h2><i class="fas fa-file-archive"></i> ملفات APK الموجودة (<?= count($apk_files) ?>)</h2>
            
            <?php if (empty($apk_files)): ?>
                <p style="text-align: center; color: #666; padding: 20px;">
                    <i class="fas fa-folder-open" style="font-size: 2rem; display: block; margin-bottom: 10px;"></i>
                    لا توجد ملفات APK
                </p>
            <?php else: ?>
                <div class="file-list">
                    <?php foreach ($apk_files as $file): ?>
                    <div class="status-item">
                        <div>
                            <strong><?= htmlspecialchars($file['name']) ?></strong><br>
                            <small>
                                الحجم: <?= formatBytes($file['size']) ?> | 
                                التاريخ: <?= date('Y-m-d H:i:s', $file['date']) ?>
                            </small>
                        </div>
                        <div>
                            <span class="<?= $file['readable'] ? 'status-ok' : 'status-error' ?>">
                                <i class="fas fa-<?= $file['readable'] ? 'check' : 'times' ?>"></i>
                                <?= $file['readable'] ? 'قابل للقراءة' : 'غير قابل للقراءة' ?>
                            </span>
                            <br>
                            <a href="downloads/<?= urlencode($file['name']) ?>" 
                               class="test-btn" download style="font-size: 0.8rem; padding: 5px 10px;">
                                <i class="fas fa-download"></i> تحميل
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="debug-section">
            <h2><i class="fas fa-cog"></i> إعدادات PHP</h2>
            
            <?php foreach ($php_settings as $setting => $value): ?>
            <div class="status-item">
                <span><?= $setting ?></span>
                <span style="font-family: monospace;"><?= $value ?: 'غير محدد' ?></span>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="debug-section">
            <h2><i class="fas fa-puzzle-piece"></i> المكتبات المطلوبة</h2>
            
            <?php foreach ($extensions as $ext => $loaded): ?>
            <div class="status-item">
                <span>مكتبة <?= $ext ?></span>
                <span class="<?= $loaded ? 'status-ok' : 'status-error' ?>">
                    <i class="fas fa-<?= $loaded ? 'check' : 'times' ?>"></i>
                    <?= $loaded ? 'مثبتة' : 'غير مثبتة' ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="debug-section">
            <h2><i class="fas fa-vial"></i> اختبارات التحميل</h2>
            
            <p>اختبر وظائف التحميل المختلفة:</p>
            
            <a href="?test_download=1" class="test-btn">
                <i class="fas fa-download"></i> اختبار تحميل ملف نصي
            </a>
            
            <a href="test_download.php" class="test-btn">
                <i class="fas fa-download"></i> اختبار تحميل PHP
            </a>
            
            <button onclick="testJSDownload()" class="test-btn">
                <i class="fas fa-download"></i> اختبار تحميل JavaScript
            </button>
        </div>

        <div class="debug-section">
            <h2><i class="fas fa-tools"></i> حلول المشاكل الشائعة</h2>
            
            <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h4 style="color: #1976d2; margin-top: 0;">إذا لم يعمل التحميل:</h4>
                <ul>
                    <li>تأكد من أن مجلد downloads موجود وقابل للكتابة</li>
                    <li>تحقق من إعدادات المتصفح للتحميل التلقائي</li>
                    <li>تأكد من عدم حجب المتصفح للتحميل</li>
                    <li>جرب متصفح آخر</li>
                    <li>تحقق من مساحة القرص الصلب</li>
                </ul>
            </div>
            
            <div style="background: #fff3e0; padding: 15px; border-radius: 8px;">
                <h4 style="color: #f57c00; margin-top: 0;">إذا كان الملف فارغ أو تالف:</h4>
                <ul>
                    <li>تحقق من أذونات مجلد downloads</li>
                    <li>تأكد من توفر مساحة كافية</li>
                    <li>تحقق من إعدادات PHP للذاكرة ووقت التنفيذ</li>
                    <li>راجع سجل الأخطاء</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" class="test-btn">
                <i class="fas fa-home"></i> العودة للرئيسية
            </a>
            <a href="check.php" class="test-btn">
                <i class="fas fa-stethoscope"></i> فحص النظام
            </a>
            <button onclick="location.reload()" class="test-btn">
                <i class="fas fa-sync-alt"></i> تحديث
            </button>
        </div>
    </div>

    <script>
        function testJSDownload() {
            const content = 'اختبار تحميل JavaScript\nTest JavaScript Download\nالوقت: ' + new Date().toLocaleString();
            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = 'js_test_download.txt';
            link.style.display = 'none';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            window.URL.revokeObjectURL(url);
            
            alert('تم بدء اختبار التحميل بـ JavaScript');
        }
    </script>
</body>
</html>
