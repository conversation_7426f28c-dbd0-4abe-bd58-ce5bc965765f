# محول الروابط إلى تطبيقات APK

مشروع لتحويل أي موقع إلكتروني إلى تطبيق أندرويد APK بسهولة.

## المميزات

- ✅ واجهة مستخدم باللغة العربية
- ✅ تحويل أي موقع إلى تطبيق WebView
- ✅ تخصيص أيقونة التطبيق
- ✅ إعدادات متقدمة للتطبيق
- ✅ دعم اتجاهات الشاشة المختلفة
- ✅ تخصيص ألوان الموضوع
- ✅ وضع ملء الشاشة
- ✅ إعدادات User Agent مخصصة

## متطلبات التشغيل

### الخادم
- PHP 7.4 أو أحدث
- مكتبة GD للصور
- مكتبة ZipArchive
- دعم رفع الملفات

### المتصفح
- متصفح حديث يدعم HTML5
- JavaScript مفعل

## طريقة التثبيت

### 1. تثبيت PHP (إذا لم يكن مثبتاً)

#### على Windows:
```bash
# تحميل XAMPP أو تثبيت PHP مباشرة
# من https://www.php.net/downloads.php
```

#### على macOS:
```bash
brew install php
```

#### على Ubuntu/Debian:
```bash
sudo apt update
sudo apt install php php-gd php-zip
```

### 2. تشغيل المشروع

```bash
# الانتقال إلى مجلد المشروع
cd "تحويل الرابط الى تطبيق"

# تشغيل خادم PHP المحلي
php -S localhost:8000

# أو استخدام منفذ آخر
php -S localhost:3000
```

### 3. فتح المتصفح
افتح المتصفح وانتقل إلى: `http://localhost:8000`

## طريقة الاستخدام

1. **أدخل رابط الموقع**: ضع رابط الموقع الذي تريد تحويله
2. **اسم التطبيق**: أدخل اسم التطبيق المطلوب (سيتم إنشاء اسم الحزمة تلقائياً)
3. **اختر الأيقونة** (اختياري): ارفع صورة للأيقونة (PNG/JPG)
4. **إعدادات التطبيق**: 
   - إصدار التطبيق
   - اتجاه الشاشة
   - لون الموضوع
   - وضع ملء الشاشة
5. **إعدادات متقدمة**:
   - User Agent مخصص
   - شاشة البداية
   - السماح بالتكبير
6. **اضغط "إنشاء تطبيق APK"**
7. **حمل الملف** عند اكتمال العملية

### ✨ ميزة إنشاء اسم الحزمة التلقائي

لا تحتاج لكتابة اسم الحزمة بنفسك! النظام يقوم بإنشائه تلقائياً:

- **مثال 1**: "متجر الكتب" → `com.webtoapp.store`
- **مثال 2**: "مطعم الأصالة" → `com.webtoapp.restaurant`
- **مثال 3**: "عيادة الدكتور أحمد" → `com.webtoapp.clinic`

**للتخصيص اليدوي**: اضغط زر "تخصيص" بجانب حقل اسم الحزمة

## هيكل المشروع

```
تحويل الرابط الى تطبيق/
├── index.html          # الواجهة الرئيسية
├── style.css           # ملف الأنماط
├── script.js           # منطق JavaScript
├── generate.php        # معالج الخادم
├── temp/              # مجلد الملفات المؤقتة
├── downloads/         # مجلد ملفات APK المُنشأة
└── README.md          # هذا الملف
```

## الملفات المُنشأة

عند إنشاء APK، يتم إنشاء:
- `AndroidManifest.xml` - ملف البيان
- `MainActivity.java` - النشاط الرئيسي
- `activity_main.xml` - تخطيط الواجهة
- ملفات الموارد (الألوان، النصوص، الأنماط)
- أيقونات بأحجام مختلفة
- ملف `build.gradle` للبناء

## ملاحظات مهمة

⚠️ **تنبيه**: هذا المشروع ينشئ ملف ZIP بصيغة APK وليس APK حقيقي قابل للتثبيت. لإنشاء APK حقيقي تحتاج إلى:
- Android SDK
- أدوات البناء (Build Tools)
- توقيع التطبيق

## التطوير المستقبلي

- [ ] دمج Android SDK للبناء الحقيقي
- [ ] إضافة المزيد من الإعدادات
- [ ] دعم PWA
- [ ] تحسين الأمان
- [ ] إضافة قاعدة بيانات للمشاريع
- [ ] واجهة إدارة المشاريع

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## الدعم

إذا واجهت أي مشاكل:
1. تأكد من تثبيت PHP بشكل صحيح
2. تحقق من أن المجلدات `temp/` و `downloads/` قابلة للكتابة
3. تأكد من تفعيل مكتبات GD و ZipArchive

---

**تم تطويره بـ ❤️ لمجتمع المطورين العرب**
