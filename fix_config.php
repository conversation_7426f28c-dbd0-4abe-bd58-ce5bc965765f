<?php
/**
 * إصلاح سريع لمشكلة config.php
 * Quick fix for config.php issue
 */

// إنشاء ملف config.php مبسط إذا لم يكن موجوداً
$config_file = __DIR__ . '/config.php';

if (!file_exists($config_file)) {
    $config_content = <<<'PHP'
<?php
/**
 * ملف إعدادات مبسط
 * Simplified Configuration File
 */

// إعدادات عامة
define('APP_NAME', 'محول الروابط إلى تطبيقات APK');
define('APP_VERSION', '1.0.1');

// إعدادات المجلدات
define('TEMP_DIR', __DIR__ . '/temp');
define('DOWNLOADS_DIR', __DIR__ . '/downloads');

// إعدادات الملفات
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('MAX_EXECUTION_TIME', 300); // 5 دقائق

// إعدادات الأمان
define('ENABLE_DEBUG', false);
define('LOG_ERRORS', true);

// دالة تهيئة المجلدات
function initializeDirectories() {
    $directories = [
        TEMP_DIR,
        DOWNLOADS_DIR,
        __DIR__ . '/logs'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new Exception("فشل في إنشاء المجلد: $dir");
            }
        }
    }
}

// دالة تسجيل الأخطاء
function logError($message, $file = null, $line = null) {
    if (!LOG_ERRORS) return;
    
    $log_file = __DIR__ . '/logs/error.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message";
    
    if ($file && $line) {
        $log_message .= " في $file:$line";
    }
    
    $log_message .= PHP_EOL;
    
    @file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
}

// دالة تسجيل العمليات
function logActivity($action, $details = '') {
    $log_file = __DIR__ . '/logs/activity.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    $log_message = "[$timestamp] IP: $ip | Action: $action | Details: $details" . PHP_EOL;
    
    @file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
}

// دالة تنظيف الملفات القديمة
function cleanupOldFiles() {
    // تنظيف بسيط للملفات الأقدم من 24 ساعة
    $directories = [TEMP_DIR, DOWNLOADS_DIR];
    $max_age = 24 * 60 * 60; // 24 ساعة
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) continue;
        
        $files = glob($dir . '/*');
        foreach ($files as $file) {
            if (is_file($file) && (time() - filemtime($file)) > $max_age) {
                @unlink($file);
            }
        }
    }
}

// دالة للحصول على حجم المجلد
function getFolderSize($dir) {
    if (!is_dir($dir)) return 0;
    
    $size = 0;
    $files = glob($dir . '/*');
    foreach ($files as $file) {
        if (is_file($file)) {
            $size += filesize($file);
        }
    }
    return $size;
}

// تهيئة المشروع
try {
    initializeDirectories();
} catch (Exception $e) {
    logError("خطأ في تهيئة المشروع: " . $e->getMessage());
    if (ENABLE_DEBUG) {
        die("خطأ في تهيئة المشروع: " . $e->getMessage());
    }
}

// إعدادات PHP
ini_set('upload_max_filesize', '10M');
ini_set('post_max_size', '15M');
ini_set('max_execution_time', MAX_EXECUTION_TIME);
ini_set('memory_limit', '256M');

if (LOG_ERRORS) {
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/logs/php_errors.log');
}

if (!ENABLE_DEBUG) {
    ini_set('display_errors', 0);
    error_reporting(0);
}
?>
PHP;

    if (file_put_contents($config_file, $config_content)) {
        echo "✅ تم إنشاء ملف config.php بنجاح\n";
    } else {
        echo "❌ فشل في إنشاء ملف config.php\n";
    }
} else {
    echo "ℹ️ ملف config.php موجود بالفعل\n";
}

// إنشاء المجلدات المطلوبة
$directories = [
    __DIR__ . '/temp',
    __DIR__ . '/downloads', 
    __DIR__ . '/logs'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ تم إنشاء المجلد: $dir\n";
        } else {
            echo "❌ فشل في إنشاء المجلد: $dir\n";
        }
    } else {
        echo "ℹ️ المجلد موجود: $dir\n";
    }
}

// التحقق من الأذونات
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ المجلد قابل للكتابة: $dir\n";
        } else {
            echo "⚠️ المجلد غير قابل للكتابة: $dir\n";
            // محاولة إصلاح الأذونات
            if (chmod($dir, 0755)) {
                echo "✅ تم إصلاح أذونات: $dir\n";
            } else {
                echo "❌ فشل في إصلاح أذونات: $dir\n";
            }
        }
    }
}

echo "\n🔧 تم الانتهاء من الإصلاح!\n";
echo "يمكنك الآن تجربة إنشاء تطبيق APK.\n";
?>
