<?php
// تضمين ملف التكوين
require_once 'config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // تسجيل بداية العملية
    logActivity('APK_GENERATION_START', 'بدء عملية إنشاء APK');

    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }

    // التحقق من البيانات المطلوبة
    $website_url = filter_input(INPUT_POST, 'website_url', FILTER_VALIDATE_URL);
    $app_name = filter_input(INPUT_POST, 'app_name', FILTER_SANITIZE_STRING);

    if (!$website_url || !$app_name) {
        throw new Exception('البيانات المطلوبة مفقودة أو غير صحيحة');
    }

    // تسجيل البيانات المستلمة
    logActivity('APK_DATA_RECEIVED', "App: $app_name, URL: $website_url");
    
    // جمع البيانات
    $app_data = [
        'website_url' => $website_url,
        'app_name' => trim($app_name),
        'package_name' => filter_input(INPUT_POST, 'package_name', FILTER_SANITIZE_STRING) ?: generatePackageName($app_name),
        'app_version' => filter_input(INPUT_POST, 'app_version', FILTER_SANITIZE_STRING) ?: '1.0',
        'orientation' => filter_input(INPUT_POST, 'orientation', FILTER_SANITIZE_STRING) ?: 'portrait',
        'theme_color' => filter_input(INPUT_POST, 'theme_color', FILTER_SANITIZE_STRING) ?: '#2196F3',
        'fullscreen' => filter_input(INPUT_POST, 'fullscreen', FILTER_SANITIZE_STRING) === '1',
        'user_agent' => filter_input(INPUT_POST, 'user_agent', FILTER_SANITIZE_STRING),
        'splash_screen' => filter_input(INPUT_POST, 'splash_screen', FILTER_SANITIZE_STRING) !== '0',
        'allow_zoom' => filter_input(INPUT_POST, 'allow_zoom', FILTER_SANITIZE_STRING) !== '0'
    ];
    
    // إنشاء مجلد مؤقت للمشروع
    $project_id = uniqid('app_', true);
    $temp_dir = __DIR__ . '/temp/' . $project_id;
    
    if (!createDirectory($temp_dir)) {
        throw new Exception('فشل في إنشاء المجلد المؤقت');
    }
    
    // معالجة الأيقونة
    $icon_path = null;
    if (isset($_FILES['app_icon']) && $_FILES['app_icon']['error'] === UPLOAD_ERR_OK) {
        $icon_path = handleIconUpload($_FILES['app_icon'], $temp_dir);
    }
    
    // إنشاء ملفات المشروع
    createProjectFiles($temp_dir, $app_data, $icon_path);
    
    // إنشاء ملف APK
    $apk_path = buildAPK($temp_dir, $app_data);
    
    if (!$apk_path || !file_exists($apk_path)) {
        throw new Exception('فشل في إنشاء ملف APK');
    }
    
    // نقل ملف APK إلى مجلد التحميلات
    $downloads_dir = __DIR__ . '/downloads';
    createDirectory($downloads_dir);
    
    $final_filename = sanitizeFilename($app_data['app_name']) . '.apk';
    $final_path = $downloads_dir . '/' . $final_filename;
    
    if (!copy($apk_path, $final_path)) {
        throw new Exception('فشل في نسخ ملف APK');
    }
    
    // تنظيف الملفات المؤقتة
    cleanupDirectory($temp_dir);

    // تسجيل نجاح العملية
    logActivity('APK_GENERATION_SUCCESS', "تم إنشاء $final_filename بنجاح");

    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'message' => 'تم إنشاء التطبيق بنجاح',
        'download_url' => 'downloads/' . $final_filename,
        'filename' => $final_filename
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // تسجيل الخطأ
    logError('خطأ في إنشاء APK: ' . $e->getMessage(), $e->getFile(), $e->getLine());
    logActivity('APK_GENERATION_ERROR', $e->getMessage());

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

// دالة إنشاء اسم الحزمة
function generatePackageName($app_name) {
    // إزالة الأحرف العربية والرموز الخاصة
    $clean_name = preg_replace('/[\x{0600}-\x{06FF}]/u', '', $app_name); // إزالة العربية
    $clean_name = preg_replace('/[^a-zA-Z0-9]/', '', strtolower($clean_name)); // إبقاء الإنجليزية والأرقام فقط

    // إذا لم يبق شيء، استخدم timestamp
    if (empty($clean_name)) {
        $clean_name = 'app' . substr(time(), -6);
    } else {
        $clean_name = substr($clean_name, 0, 15);
    }

    return 'com.webtoapp.' . $clean_name;
}

// دالة إنشاء المجلد
function createDirectory($path) {
    if (!is_dir($path)) {
        return mkdir($path, 0755, true);
    }
    return true;
}

// دالة معالجة رفع الأيقونة
function handleIconUpload($file, $temp_dir) {
    $allowed_types = ['image/png', 'image/jpeg', 'image/jpg'];
    
    if (!in_array($file['type'], $allowed_types)) {
        throw new Exception('نوع ملف الأيقونة غير مدعوم');
    }
    
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception('حجم ملف الأيقونة كبير جداً');
    }
    
    $icon_path = $temp_dir . '/icon.png';
    
    if (!move_uploaded_file($file['tmp_name'], $icon_path)) {
        throw new Exception('فشل في رفع ملف الأيقونة');
    }
    
    // تحويل الأيقونة إلى PNG وتغيير حجمها
    resizeIcon($icon_path);
    
    return $icon_path;
}

// دالة تغيير حجم الأيقونة
function resizeIcon($icon_path) {
    if (!extension_loaded('gd')) {
        return; // تجاهل إذا لم تكن مكتبة GD متاحة
    }
    
    $image_info = getimagesize($icon_path);
    if (!$image_info) return;
    
    $width = $image_info[0];
    $height = $image_info[1];
    $type = $image_info[2];
    
    // إنشاء الصورة الأصلية
    switch ($type) {
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($icon_path);
            break;
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($icon_path);
            break;
        default:
            return;
    }
    
    // إنشاء صورة جديدة بحجم 512x512
    $new_image = imagecreatetruecolor(512, 512);
    imagealphablending($new_image, false);
    imagesavealpha($new_image, true);
    
    // نسخ وتغيير حجم الصورة
    imagecopyresampled($new_image, $source, 0, 0, 0, 0, 512, 512, $width, $height);
    
    // حفظ الصورة الجديدة
    imagepng($new_image, $icon_path);
    
    // تنظيف الذاكرة
    imagedestroy($source);
    imagedestroy($new_image);
}

// دالة إنشاء ملفات المشروع
function createProjectFiles($temp_dir, $app_data, $icon_path) {
    // إنشاء هيكل المجلدات
    $dirs = [
        'app/src/main',
        'app/src/main/java/' . str_replace('.', '/', $app_data['package_name']),
        'app/src/main/res/layout',
        'app/src/main/res/values',
        'app/src/main/res/mipmap-hdpi',
        'app/src/main/res/mipmap-mdpi',
        'app/src/main/res/mipmap-xhdpi',
        'app/src/main/res/mipmap-xxhdpi',
        'app/src/main/res/mipmap-xxxhdpi'
    ];
    
    foreach ($dirs as $dir) {
        createDirectory($temp_dir . '/' . $dir);
    }
    
    // إنشاء ملف AndroidManifest.xml
    createManifestFile($temp_dir, $app_data);
    
    // إنشاء ملف MainActivity.java
    createMainActivityFile($temp_dir, $app_data);
    
    // إنشاء ملف activity_main.xml
    createLayoutFile($temp_dir, $app_data);
    
    // إنشاء ملفات القيم
    createValuesFiles($temp_dir, $app_data);
    
    // إنشاء ملف build.gradle
    createBuildGradleFile($temp_dir, $app_data);
    
    // نسخ الأيقونة
    if ($icon_path) {
        copyIconToResources($icon_path, $temp_dir);
    } else {
        createDefaultIcon($temp_dir);
    }
}

// دالة إنشاء ملف AndroidManifest.xml
function createManifestFile($temp_dir, $app_data) {
    $orientation = $app_data['orientation'];
    $fullscreen = $app_data['fullscreen'] ? 'android:theme="@android:style/Theme.NoTitleBar.Fullscreen"' : '';
    
    $manifest = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="{$app_data['package_name']}"
    android:versionCode="1"
    android:versionName="{$app_data['app_version']}">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="{$app_data['app_name']}"
        android:theme="@style/AppTheme">
        
        <activity
            android:name=".MainActivity"
            android:screenOrientation="{$orientation}"
            {$fullscreen}
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
XML;
    
    file_put_contents($temp_dir . '/app/src/main/AndroidManifest.xml', $manifest);
}

// دالة إنشاء ملف MainActivity.java
function createMainActivityFile($temp_dir, $app_data) {
    $package_name = $app_data['package_name'];
    $website_url = $app_data['website_url'];
    $user_agent = $app_data['user_agent'] ?: 'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36';
    $allow_zoom = $app_data['allow_zoom'] ? 'true' : 'false';

    $java_code = <<<JAVA
package {$package_name};

import android.app.Activity;
import android.os.Bundle;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.webkit.WebSettings;
import android.view.KeyEvent;

public class MainActivity extends Activity {
    private WebView webView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        webView = findViewById(R.id.webview);
        setupWebView();
        webView.loadUrl("{$website_url}");
    }

    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setUserAgentString("{$user_agent}");
        webSettings.setSupportZoom({$allow_zoom});
        webSettings.setBuiltInZoomControls({$allow_zoom});
        webSettings.setDisplayZoomControls(false);

        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && webView.canGoBack()) {
            webView.goBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
}
JAVA;

    $java_path = $temp_dir . '/app/src/main/java/' . str_replace('.', '/', $package_name) . '/MainActivity.java';
    file_put_contents($java_path, $java_code);
}

// دالة إنشاء ملف activity_main.xml
function createLayoutFile($temp_dir, $app_data) {
    $layout = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
XML;

    file_put_contents($temp_dir . '/app/src/main/res/layout/activity_main.xml', $layout);
}

// دالة إنشاء ملفات القيم
function createValuesFiles($temp_dir, $app_data) {
    $theme_color = $app_data['theme_color'];

    // ملف colors.xml
    $colors = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorPrimary">{$theme_color}</color>
    <color name="colorPrimaryDark">{$theme_color}</color>
    <color name="colorAccent">{$theme_color}</color>
</resources>
XML;

    file_put_contents($temp_dir . '/app/src/main/res/values/colors.xml', $colors);

    // ملف strings.xml
    $strings = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">{$app_data['app_name']}</string>
</resources>
XML;

    file_put_contents($temp_dir . '/app/src/main/res/values/strings.xml', $strings);

    // ملف styles.xml
    $styles = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppTheme" parent="android:Theme.Holo.Light.DarkActionBar">
        <item name="android:colorPrimary">@color/colorPrimary</item>
        <item name="android:colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="android:colorAccent">@color/colorAccent</item>
    </style>
</resources>
XML;

    file_put_contents($temp_dir . '/app/src/main/res/values/styles.xml', $styles);
}

// دالة إنشاء ملف build.gradle
function createBuildGradleFile($temp_dir, $app_data) {
    $gradle = <<<GRADLE
apply plugin: 'com.android.application'

android {
    compileSdkVersion 33
    buildToolsVersion "33.0.0"

    defaultConfig {
        applicationId "{$app_data['package_name']}"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "{$app_data['app_version']}"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
}
GRADLE;

    file_put_contents($temp_dir . '/app/build.gradle', $gradle);

    // ملف build.gradle الرئيسي
    $root_gradle = <<<GRADLE
buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}
GRADLE;

    file_put_contents($temp_dir . '/build.gradle', $root_gradle);
}

// دالة نسخ الأيقونة إلى الموارد
function copyIconToResources($icon_path, $temp_dir) {
    $densities = ['hdpi', 'mdpi', 'xhdpi', 'xxhdpi', 'xxxhdpi'];

    foreach ($densities as $density) {
        $dest_path = $temp_dir . "/app/src/main/res/mipmap-{$density}/ic_launcher.png";
        copy($icon_path, $dest_path);
    }
}

// دالة إنشاء أيقونة افتراضية
function createDefaultIcon($temp_dir) {
    // إنشاء أيقونة بسيطة باستخدام GD
    if (!extension_loaded('gd')) {
        return;
    }

    $image = imagecreatetruecolor(512, 512);
    $bg_color = imagecolorallocate($image, 33, 150, 243);
    $text_color = imagecolorallocate($image, 255, 255, 255);
    $border_color = imagecolorallocate($image, 255, 255, 255);

    // ملء الخلفية
    imagefill($image, 0, 0, $bg_color);

    // رسم دائرة في المنتصف
    $center_x = 256;
    $center_y = 256;
    $radius = 180;

    // رسم دائرة خارجية
    imageellipse($image, $center_x, $center_y, $radius * 2, $radius * 2, $border_color);
    imageellipse($image, $center_x, $center_y, ($radius - 5) * 2, ($radius - 5) * 2, $border_color);

    // رسم النص
    $font_size = 5; // أكبر حجم خط متاح
    $text = 'APP';

    // حساب موقع النص
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_height = imagefontheight($font_size);
    $x = ($center_x) - ($text_width / 2);
    $y = ($center_y) - ($text_height / 2);

    imagestring($image, $font_size, $x, $y, $text, $text_color);

    // رسم أيقونة هاتف بسيطة
    $phone_x = $center_x - 30;
    $phone_y = $center_y + 40;
    $phone_width = 60;
    $phone_height = 80;

    // جسم الهاتف
    imagerectangle($image, $phone_x, $phone_y, $phone_x + $phone_width, $phone_y + $phone_height, $text_color);
    imagerectangle($image, $phone_x + 1, $phone_y + 1, $phone_x + $phone_width - 1, $phone_y + $phone_height - 1, $text_color);

    // شاشة الهاتف
    $screen_margin = 8;
    imagerectangle($image,
        $phone_x + $screen_margin,
        $phone_y + $screen_margin,
        $phone_x + $phone_width - $screen_margin,
        $phone_y + $phone_height - $screen_margin - 15,
        $text_color
    );

    $icon_path = $temp_dir . '/icon.png';
    imagepng($image, $icon_path);
    imagedestroy($image);

    copyIconToResources($icon_path, $temp_dir);
}

// دالة بناء ملف APK
function buildAPK($temp_dir, $app_data) {
    // هذه دالة مبسطة - في الواقع تحتاج إلى Android SDK
    // سنقوم بإنشاء ملف ZIP يحتوي على ملفات المشروع مع بنية APK صحيحة

    $apk_path = $temp_dir . '/' . sanitizeFilename($app_data['app_name']) . '.apk';

    // التحقق من وجود مكتبة ZipArchive
    if (!class_exists('ZipArchive')) {
        throw new Exception('مكتبة ZIP غير متاحة على الخادم');
    }

    // إنشاء ملف ZIP
    $zip = new ZipArchive();
    $result = $zip->open($apk_path, ZipArchive::CREATE | ZipArchive::OVERWRITE);

    if ($result !== TRUE) {
        throw new Exception('فشل في إنشاء ملف APK: ' . getZipError($result));
    }

    try {
        // إضافة ملف AndroidManifest.xml في الجذر
        $manifest_content = file_get_contents($temp_dir . '/app/src/main/AndroidManifest.xml');
        $zip->addFromString('AndroidManifest.xml', $manifest_content);

        // إضافة ملفات المشروع إلى ZIP
        addDirectoryToZip($temp_dir . '/app', $zip, '');

        // إضافة ملفات META-INF (مطلوبة لـ APK)
        $zip->addFromString('META-INF/MANIFEST.MF', "Manifest-Version: 1.0\nCreated-By: WebToAPK Generator\n");

        // إضافة معلومات إضافية
        $zip->addFromString('assets/app_info.txt',
            "App Name: {$app_data['app_name']}\n" .
            "Package: {$app_data['package_name']}\n" .
            "Version: {$app_data['app_version']}\n" .
            "Website: {$app_data['website_url']}\n" .
            "Generated: " . date('Y-m-d H:i:s')
        );

        $zip->close();

        // التحقق من إنشاء الملف بنجاح
        if (!file_exists($apk_path) || filesize($apk_path) == 0) {
            throw new Exception('فشل في إنشاء ملف APK صحيح');
        }

        return $apk_path;

    } catch (Exception $e) {
        $zip->close();
        throw $e;
    }
}

// دالة للحصول على رسالة خطأ ZIP
function getZipError($code) {
    switch($code) {
        case ZipArchive::ER_OK: return 'لا يوجد خطأ';
        case ZipArchive::ER_MULTIDISK: return 'أقراص متعددة غير مدعومة';
        case ZipArchive::ER_RENAME: return 'فشل في إعادة التسمية';
        case ZipArchive::ER_CLOSE: return 'فشل في إغلاق الملف';
        case ZipArchive::ER_SEEK: return 'خطأ في البحث';
        case ZipArchive::ER_READ: return 'خطأ في القراءة';
        case ZipArchive::ER_WRITE: return 'خطأ في الكتابة';
        case ZipArchive::ER_CRC: return 'خطأ CRC';
        case ZipArchive::ER_ZIPCLOSED: return 'الملف مغلق';
        case ZipArchive::ER_NOENT: return 'الملف غير موجود';
        case ZipArchive::ER_EXISTS: return 'الملف موجود بالفعل';
        case ZipArchive::ER_OPEN: return 'لا يمكن فتح الملف';
        case ZipArchive::ER_TMPOPEN: return 'فشل في إنشاء ملف مؤقت';
        case ZipArchive::ER_ZLIB: return 'خطأ Zlib';
        case ZipArchive::ER_MEMORY: return 'خطأ في الذاكرة';
        case ZipArchive::ER_CHANGED: return 'الملف تم تغييره';
        case ZipArchive::ER_COMPNOTSUPP: return 'طريقة الضغط غير مدعومة';
        case ZipArchive::ER_EOF: return 'نهاية الملف غير متوقعة';
        case ZipArchive::ER_INVAL: return 'معامل غير صحيح';
        case ZipArchive::ER_NOZIP: return 'ليس ملف zip';
        case ZipArchive::ER_INTERNAL: return 'خطأ داخلي';
        case ZipArchive::ER_INCONS: return 'أرشيف غير متسق';
        case ZipArchive::ER_REMOVE: return 'لا يمكن إزالة الملف';
        case ZipArchive::ER_DELETED: return 'الملف محذوف';
        default: return "خطأ غير معروف: $code";
    }
}

// دالة إضافة مجلد إلى ZIP
function addDirectoryToZip($dir, $zip, $prefix = '') {
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir),
        RecursiveIteratorIterator::LEAVES_ONLY
    );

    foreach ($files as $file) {
        if (!$file->isDir()) {
            $filePath = $file->getRealPath();
            $relativePath = $prefix . substr($filePath, strlen($dir) + 1);
            $zip->addFile($filePath, $relativePath);
        }
    }
}

// دالة تنظيف اسم الملف
function sanitizeFilename($filename) {
    return preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);
}

// دالة تنظيف المجلد
function cleanupDirectory($dir) {
    if (!is_dir($dir)) return;

    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );

    foreach ($files as $file) {
        if ($file->isDir()) {
            rmdir($file->getRealPath());
        } else {
            unlink($file->getRealPath());
        }
    }

    rmdir($dir);
}
?>
