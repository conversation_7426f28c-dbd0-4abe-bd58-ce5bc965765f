<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// إعدادات الأمان
ini_set('upload_max_filesize', '5M');
ini_set('post_max_size', '10M');
ini_set('max_execution_time', 300); // 5 دقائق

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }
    
    // التحقق من البيانات المطلوبة
    $website_url = filter_input(INPUT_POST, 'website_url', FILTER_VALIDATE_URL);
    $app_name = filter_input(INPUT_POST, 'app_name', FILTER_SANITIZE_STRING);
    
    if (!$website_url || !$app_name) {
        throw new Exception('البيانات المطلوبة مفقودة أو غير صحيحة');
    }
    
    // جمع البيانات
    $app_data = [
        'website_url' => $website_url,
        'app_name' => trim($app_name),
        'package_name' => filter_input(INPUT_POST, 'package_name', FILTER_SANITIZE_STRING) ?: generatePackageName($app_name),
        'app_version' => filter_input(INPUT_POST, 'app_version', FILTER_SANITIZE_STRING) ?: '1.0',
        'orientation' => filter_input(INPUT_POST, 'orientation', FILTER_SANITIZE_STRING) ?: 'portrait',
        'theme_color' => filter_input(INPUT_POST, 'theme_color', FILTER_SANITIZE_STRING) ?: '#2196F3',
        'fullscreen' => filter_input(INPUT_POST, 'fullscreen', FILTER_SANITIZE_STRING) === '1',
        'user_agent' => filter_input(INPUT_POST, 'user_agent', FILTER_SANITIZE_STRING),
        'splash_screen' => filter_input(INPUT_POST, 'splash_screen', FILTER_SANITIZE_STRING) !== '0',
        'allow_zoom' => filter_input(INPUT_POST, 'allow_zoom', FILTER_SANITIZE_STRING) !== '0'
    ];
    
    // إنشاء مجلد مؤقت للمشروع
    $project_id = uniqid('app_', true);
    $temp_dir = __DIR__ . '/temp/' . $project_id;
    
    if (!createDirectory($temp_dir)) {
        throw new Exception('فشل في إنشاء المجلد المؤقت');
    }
    
    // معالجة الأيقونة
    $icon_path = null;
    if (isset($_FILES['app_icon']) && $_FILES['app_icon']['error'] === UPLOAD_ERR_OK) {
        $icon_path = handleIconUpload($_FILES['app_icon'], $temp_dir);
    }
    
    // إنشاء ملفات المشروع
    createProjectFiles($temp_dir, $app_data, $icon_path);
    
    // إنشاء ملف APK
    $apk_path = buildAPK($temp_dir, $app_data);
    
    if (!$apk_path || !file_exists($apk_path)) {
        throw new Exception('فشل في إنشاء ملف APK');
    }
    
    // نقل ملف APK إلى مجلد التحميلات
    $downloads_dir = __DIR__ . '/downloads';
    createDirectory($downloads_dir);
    
    $final_filename = sanitizeFilename($app_data['app_name']) . '.apk';
    $final_path = $downloads_dir . '/' . $final_filename;
    
    if (!copy($apk_path, $final_path)) {
        throw new Exception('فشل في نسخ ملف APK');
    }
    
    // تنظيف الملفات المؤقتة
    cleanupDirectory($temp_dir);
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'message' => 'تم إنشاء التطبيق بنجاح',
        'download_url' => 'downloads/' . $final_filename,
        'filename' => $final_filename
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

// دالة إنشاء اسم الحزمة
function generatePackageName($app_name) {
    $clean_name = preg_replace('/[^a-zA-Z0-9]/', '', strtolower($app_name));
    return 'com.webtoapp.' . substr($clean_name, 0, 20);
}

// دالة إنشاء المجلد
function createDirectory($path) {
    if (!is_dir($path)) {
        return mkdir($path, 0755, true);
    }
    return true;
}

// دالة معالجة رفع الأيقونة
function handleIconUpload($file, $temp_dir) {
    $allowed_types = ['image/png', 'image/jpeg', 'image/jpg'];
    
    if (!in_array($file['type'], $allowed_types)) {
        throw new Exception('نوع ملف الأيقونة غير مدعوم');
    }
    
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception('حجم ملف الأيقونة كبير جداً');
    }
    
    $icon_path = $temp_dir . '/icon.png';
    
    if (!move_uploaded_file($file['tmp_name'], $icon_path)) {
        throw new Exception('فشل في رفع ملف الأيقونة');
    }
    
    // تحويل الأيقونة إلى PNG وتغيير حجمها
    resizeIcon($icon_path);
    
    return $icon_path;
}

// دالة تغيير حجم الأيقونة
function resizeIcon($icon_path) {
    if (!extension_loaded('gd')) {
        return; // تجاهل إذا لم تكن مكتبة GD متاحة
    }
    
    $image_info = getimagesize($icon_path);
    if (!$image_info) return;
    
    $width = $image_info[0];
    $height = $image_info[1];
    $type = $image_info[2];
    
    // إنشاء الصورة الأصلية
    switch ($type) {
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($icon_path);
            break;
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($icon_path);
            break;
        default:
            return;
    }
    
    // إنشاء صورة جديدة بحجم 512x512
    $new_image = imagecreatetruecolor(512, 512);
    imagealphablending($new_image, false);
    imagesavealpha($new_image, true);
    
    // نسخ وتغيير حجم الصورة
    imagecopyresampled($new_image, $source, 0, 0, 0, 0, 512, 512, $width, $height);
    
    // حفظ الصورة الجديدة
    imagepng($new_image, $icon_path);
    
    // تنظيف الذاكرة
    imagedestroy($source);
    imagedestroy($new_image);
}

// دالة إنشاء ملفات المشروع
function createProjectFiles($temp_dir, $app_data, $icon_path) {
    // إنشاء هيكل المجلدات
    $dirs = [
        'app/src/main',
        'app/src/main/java/' . str_replace('.', '/', $app_data['package_name']),
        'app/src/main/res/layout',
        'app/src/main/res/values',
        'app/src/main/res/mipmap-hdpi',
        'app/src/main/res/mipmap-mdpi',
        'app/src/main/res/mipmap-xhdpi',
        'app/src/main/res/mipmap-xxhdpi',
        'app/src/main/res/mipmap-xxxhdpi'
    ];
    
    foreach ($dirs as $dir) {
        createDirectory($temp_dir . '/' . $dir);
    }
    
    // إنشاء ملف AndroidManifest.xml
    createManifestFile($temp_dir, $app_data);
    
    // إنشاء ملف MainActivity.java
    createMainActivityFile($temp_dir, $app_data);
    
    // إنشاء ملف activity_main.xml
    createLayoutFile($temp_dir, $app_data);
    
    // إنشاء ملفات القيم
    createValuesFiles($temp_dir, $app_data);
    
    // إنشاء ملف build.gradle
    createBuildGradleFile($temp_dir, $app_data);
    
    // نسخ الأيقونة
    if ($icon_path) {
        copyIconToResources($icon_path, $temp_dir);
    } else {
        createDefaultIcon($temp_dir);
    }
}

// دالة إنشاء ملف AndroidManifest.xml
function createManifestFile($temp_dir, $app_data) {
    $orientation = $app_data['orientation'];
    $fullscreen = $app_data['fullscreen'] ? 'android:theme="@android:style/Theme.NoTitleBar.Fullscreen"' : '';
    
    $manifest = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="{$app_data['package_name']}"
    android:versionCode="1"
    android:versionName="{$app_data['app_version']}">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="{$app_data['app_name']}"
        android:theme="@style/AppTheme">
        
        <activity
            android:name=".MainActivity"
            android:screenOrientation="{$orientation}"
            {$fullscreen}
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
XML;
    
    file_put_contents($temp_dir . '/app/src/main/AndroidManifest.xml', $manifest);
}

// دالة إنشاء ملف MainActivity.java
function createMainActivityFile($temp_dir, $app_data) {
    $package_name = $app_data['package_name'];
    $website_url = $app_data['website_url'];
    $user_agent = $app_data['user_agent'] ?: 'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36';
    $allow_zoom = $app_data['allow_zoom'] ? 'true' : 'false';

    $java_code = <<<JAVA
package {$package_name};

import android.app.Activity;
import android.os.Bundle;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.webkit.WebSettings;
import android.view.KeyEvent;

public class MainActivity extends Activity {
    private WebView webView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        webView = findViewById(R.id.webview);
        setupWebView();
        webView.loadUrl("{$website_url}");
    }

    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setUserAgentString("{$user_agent}");
        webSettings.setSupportZoom({$allow_zoom});
        webSettings.setBuiltInZoomControls({$allow_zoom});
        webSettings.setDisplayZoomControls(false);

        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && webView.canGoBack()) {
            webView.goBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
}
JAVA;

    $java_path = $temp_dir . '/app/src/main/java/' . str_replace('.', '/', $package_name) . '/MainActivity.java';
    file_put_contents($java_path, $java_code);
}

// دالة إنشاء ملف activity_main.xml
function createLayoutFile($temp_dir, $app_data) {
    $layout = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
XML;

    file_put_contents($temp_dir . '/app/src/main/res/layout/activity_main.xml', $layout);
}

// دالة إنشاء ملفات القيم
function createValuesFiles($temp_dir, $app_data) {
    $theme_color = $app_data['theme_color'];

    // ملف colors.xml
    $colors = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorPrimary">{$theme_color}</color>
    <color name="colorPrimaryDark">{$theme_color}</color>
    <color name="colorAccent">{$theme_color}</color>
</resources>
XML;

    file_put_contents($temp_dir . '/app/src/main/res/values/colors.xml', $colors);

    // ملف strings.xml
    $strings = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">{$app_data['app_name']}</string>
</resources>
XML;

    file_put_contents($temp_dir . '/app/src/main/res/values/strings.xml', $strings);

    // ملف styles.xml
    $styles = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppTheme" parent="android:Theme.Holo.Light.DarkActionBar">
        <item name="android:colorPrimary">@color/colorPrimary</item>
        <item name="android:colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="android:colorAccent">@color/colorAccent</item>
    </style>
</resources>
XML;

    file_put_contents($temp_dir . '/app/src/main/res/values/styles.xml', $styles);
}

// دالة إنشاء ملف build.gradle
function createBuildGradleFile($temp_dir, $app_data) {
    $gradle = <<<GRADLE
apply plugin: 'com.android.application'

android {
    compileSdkVersion 33
    buildToolsVersion "33.0.0"

    defaultConfig {
        applicationId "{$app_data['package_name']}"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "{$app_data['app_version']}"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
}
GRADLE;

    file_put_contents($temp_dir . '/app/build.gradle', $gradle);

    // ملف build.gradle الرئيسي
    $root_gradle = <<<GRADLE
buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}
GRADLE;

    file_put_contents($temp_dir . '/build.gradle', $root_gradle);
}

// دالة نسخ الأيقونة إلى الموارد
function copyIconToResources($icon_path, $temp_dir) {
    $densities = ['hdpi', 'mdpi', 'xhdpi', 'xxhdpi', 'xxxhdpi'];

    foreach ($densities as $density) {
        $dest_path = $temp_dir . "/app/src/main/res/mipmap-{$density}/ic_launcher.png";
        copy($icon_path, $dest_path);
    }
}

// دالة إنشاء أيقونة افتراضية
function createDefaultIcon($temp_dir) {
    // إنشاء أيقونة بسيطة باستخدام GD
    if (!extension_loaded('gd')) {
        return;
    }

    $image = imagecreatetruecolor(512, 512);
    $bg_color = imagecolorallocate($image, 33, 150, 243);
    $text_color = imagecolorallocate($image, 255, 255, 255);

    imagefill($image, 0, 0, $bg_color);

    $font_size = 100;
    $text = 'APP';
    $bbox = imagettfbbox($font_size, 0, __DIR__ . '/arial.ttf', $text);

    if ($bbox) {
        $x = (512 - $bbox[4]) / 2;
        $y = (512 - $bbox[5]) / 2 + $font_size;
        imagettftext($image, $font_size, 0, $x, $y, $text_color, __DIR__ . '/arial.ttf', $text);
    } else {
        imagestring($image, 5, 200, 250, 'APP', $text_color);
    }

    $icon_path = $temp_dir . '/icon.png';
    imagepng($image, $icon_path);
    imagedestroy($image);

    copyIconToResources($icon_path, $temp_dir);
}

// دالة بناء ملف APK
function buildAPK($temp_dir, $app_data) {
    // هذه دالة مبسطة - في الواقع تحتاج إلى Android SDK
    // سنقوم بإنشاء ملف ZIP يحتوي على ملفات المشروع

    $apk_path = $temp_dir . '/' . sanitizeFilename($app_data['app_name']) . '.apk';

    // إنشاء ملف ZIP
    $zip = new ZipArchive();
    if ($zip->open($apk_path, ZipArchive::CREATE) !== TRUE) {
        throw new Exception('فشل في إنشاء ملف APK');
    }

    // إضافة ملفات المشروع إلى ZIP
    addDirectoryToZip($temp_dir . '/app', $zip, 'app/');

    $zip->close();

    return $apk_path;
}

// دالة إضافة مجلد إلى ZIP
function addDirectoryToZip($dir, $zip, $prefix = '') {
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir),
        RecursiveIteratorIterator::LEAVES_ONLY
    );

    foreach ($files as $file) {
        if (!$file->isDir()) {
            $filePath = $file->getRealPath();
            $relativePath = $prefix . substr($filePath, strlen($dir) + 1);
            $zip->addFile($filePath, $relativePath);
        }
    }
}

// دالة تنظيف اسم الملف
function sanitizeFilename($filename) {
    return preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);
}

// دالة تنظيف المجلد
function cleanupDirectory($dir) {
    if (!is_dir($dir)) return;

    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );

    foreach ($files as $file) {
        if ($file->isDir()) {
            rmdir($file->getRealPath());
        } else {
            unlink($file->getRealPath());
        }
    }

    rmdir($dir);
}
?>
