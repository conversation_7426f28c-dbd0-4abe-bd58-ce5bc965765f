<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة الخادم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-ok {
            border-right: 4px solid #4caf50;
        }
        .status-error {
            border-right: 4px solid #f44336;
        }
        .status-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .badge-success {
            background: #e8f5e8;
            color: #4caf50;
        }
        .badge-error {
            background: #ffebee;
            color: #f44336;
        }
        .data-display {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .summary-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .summary-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .refresh-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .refresh-btn:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 حالة الخادم</h1>
            <p>فحص شامل لحالة الخادم ومتطلبات النظام</p>
            <button class="refresh-btn" onclick="loadServerStatus()">
                🔄 تحديث
            </button>
            <button class="refresh-btn" onclick="testPOST()">
                📤 اختبار POST
            </button>
        </div>

        <div id="loading" class="loading">
            ⏳ جاري فحص حالة الخادم...
        </div>

        <div id="content" style="display: none;">
            <div id="summary" class="summary"></div>
            <div id="tests"></div>
        </div>
    </div>

    <script>
        // تحميل حالة الخادم عند فتح الصفحة
        window.addEventListener('load', loadServerStatus);

        async function loadServerStatus() {
            const loading = document.getElementById('loading');
            const content = document.getElementById('content');
            
            loading.style.display = 'block';
            content.style.display = 'none';
            
            try {
                const response = await fetch('server_test.php');
                const data = await response.json();
                
                displayResults(data);
                
                loading.style.display = 'none';
                content.style.display = 'block';
                
            } catch (error) {
                loading.innerHTML = `
                    ❌ فشل في الاتصال بالخادم<br>
                    <strong>الخطأ:</strong> ${error.message}
                `;
            }
        }

        async function testPOST() {
            const loading = document.getElementById('loading');
            const content = document.getElementById('content');
            
            loading.innerHTML = '📤 اختبار إرسال POST...';
            loading.style.display = 'block';
            content.style.display = 'none';
            
            try {
                const formData = new FormData();
                formData.append('test_data', 'اختبار POST');
                formData.append('timestamp', new Date().toISOString());
                
                const response = await fetch('server_test.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                displayResults(data);
                
                loading.style.display = 'none';
                content.style.display = 'block';
                
            } catch (error) {
                loading.innerHTML = `
                    ❌ فشل في اختبار POST<br>
                    <strong>الخطأ:</strong> ${error.message}
                `;
            }
        }

        function displayResults(data) {
            // عرض الملخص
            const summaryDiv = document.getElementById('summary');
            summaryDiv.innerHTML = `
                <div class="summary-item">
                    <div class="summary-number" style="color: ${data.overall_status ? '#4caf50' : '#f44336'}">
                        ${data.overall_status ? '✅' : '❌'}
                    </div>
                    <div>الحالة العامة</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" style="color: #2196F3">
                        ${data.summary.total_tests}
                    </div>
                    <div>إجمالي الاختبارات</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" style="color: #4caf50">
                        ${data.summary.passed_tests}
                    </div>
                    <div>اختبارات ناجحة</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" style="color: #f44336">
                        ${data.summary.failed_tests}
                    </div>
                    <div>اختبارات فاشلة</div>
                </div>
            `;

            // عرض تفاصيل الاختبارات
            const testsDiv = document.getElementById('tests');
            testsDiv.innerHTML = '';

            for (const [key, test] of Object.entries(data.tests)) {
                const testCard = document.createElement('div');
                testCard.className = `status-card ${test.status ? 'status-ok' : 'status-error'}`;
                
                testCard.innerHTML = `
                    <h3>
                        <span class="status-icon">${test.status ? '✅' : '❌'}</span>
                        ${test.name}
                        <span class="badge ${test.status ? 'badge-success' : 'badge-error'}">
                            ${test.status ? 'نجح' : 'فشل'}
                        </span>
                    </h3>
                    <div class="data-display">${JSON.stringify(test.data, null, 2)}</div>
                `;
                
                testsDiv.appendChild(testCard);
            }

            // إضافة معلومات إضافية
            const infoCard = document.createElement('div');
            infoCard.className = 'status-card';
            infoCard.innerHTML = `
                <h3>📊 معلومات إضافية</h3>
                <div class="test-item">
                    <span>وقت الفحص</span>
                    <span>${data.timestamp}</span>
                </div>
                <div class="test-item">
                    <span>حالة الاستجابة</span>
                    <span class="badge badge-success">200 OK</span>
                </div>
            `;
            testsDiv.appendChild(infoCard);
        }
    </script>
</body>
</html>
