<?php
/**
 * نسخة مبسطة من معالج إنشاء APK للتشخيص
 * Simplified APK Generator for Debugging
 */

// إعداد headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }

    // التحقق من البيانات المطلوبة
    $website_url = $_POST['website_url'] ?? '';
    $app_name = $_POST['app_name'] ?? '';

    if (empty($website_url) || empty($app_name)) {
        throw new Exception('البيانات المطلوبة مفقودة');
    }

    // التحقق من صحة الرابط
    if (!filter_var($website_url, FILTER_VALIDATE_URL)) {
        throw new Exception('رابط الموقع غير صحيح');
    }

    // إنشاء المجلدات المطلوبة
    $temp_dir = __DIR__ . '/temp';
    $downloads_dir = __DIR__ . '/downloads';

    if (!is_dir($temp_dir)) {
        if (!mkdir($temp_dir, 0755, true)) {
            throw new Exception('فشل في إنشاء مجلد temp');
        }
    }

    if (!is_dir($downloads_dir)) {
        if (!mkdir($downloads_dir, 0755, true)) {
            throw new Exception('فشل في إنشاء مجلد downloads');
        }
    }

    // التحقق من أذونات الكتابة
    if (!is_writable($downloads_dir)) {
        throw new Exception('مجلد downloads غير قابل للكتابة');
    }

    // إنشاء اسم حزمة بسيط
    $clean_name = preg_replace('/[^a-zA-Z0-9]/', '', strtolower($app_name));
    if (empty($clean_name)) {
        $clean_name = 'app' . time();
    }
    $package_name = 'com.webtoapp.' . substr($clean_name, 0, 15);

    // إنشاء محتوى ملف APK بسيط (ZIP)
    $apk_content = createSimpleAPK($app_name, $package_name, $website_url);

    // حفظ الملف
    $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $app_name) . '.apk';
    $filepath = $downloads_dir . '/' . $filename;

    if (file_put_contents($filepath, $apk_content) === false) {
        throw new Exception('فشل في حفظ ملف APK');
    }

    // التحقق من إنشاء الملف
    if (!file_exists($filepath) || filesize($filepath) == 0) {
        throw new Exception('فشل في إنشاء ملف APK صحيح');
    }

    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'message' => 'تم إنشاء التطبيق بنجاح',
        'download_url' => 'downloads/' . $filename,
        'filename' => $filename,
        'file_size' => filesize($filepath),
        'app_name' => $app_name,
        'package_name' => $package_name,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug_info' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * إنشاء محتوى APK بسيط
 */
function createSimpleAPK($app_name, $package_name, $website_url) {
    // إنشاء ملف ZIP بسيط يحتوي على ملفات Android أساسية
    
    // محتوى AndroidManifest.xml
    $manifest = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="{$package_name}"
    android:versionCode="1"
    android:versionName="1.0">

    <uses-permission android:name="android.permission.INTERNET" />
    
    <application
        android:allowBackup="true"
        android:label="{$app_name}">
        
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
XML;

    // محتوى MainActivity.java
    $java_code = <<<JAVA
package {$package_name};

import android.app.Activity;
import android.os.Bundle;
import android.webkit.WebView;

public class MainActivity extends Activity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        WebView webView = new WebView(this);
        webView.getSettings().setJavaScriptEnabled(true);
        webView.loadUrl("{$website_url}");
        setContentView(webView);
    }
}
JAVA;

    // معلومات التطبيق
    $app_info = <<<INFO
App Name: {$app_name}
Package: {$package_name}
Website: {$website_url}
Generated: {date('Y-m-d H:i:s')}
Generator: WebToAPK Simple Generator
INFO;

    // إنشاء محتوى ZIP
    $zip_content = '';
    
    // إضافة الملفات كنص (محاكاة ZIP)
    $zip_content .= "=== AndroidManifest.xml ===\n";
    $zip_content .= $manifest . "\n\n";
    
    $zip_content .= "=== MainActivity.java ===\n";
    $zip_content .= $java_code . "\n\n";
    
    $zip_content .= "=== app_info.txt ===\n";
    $zip_content .= $app_info . "\n\n";
    
    return $zip_content;
}
?>
