<?php
require_once 'config.php';

// التحقق من وجود كلمة مرور بسيطة للحماية
$password = $_GET['pass'] ?? '';
if ($password !== 'admin123') {
    die('غير مصرح لك بالوصول');
}

// قراءة ملفات السجل
function readLogFile($filename) {
    $filepath = __DIR__ . '/logs/' . $filename;
    if (!file_exists($filepath)) {
        return [];
    }
    
    $lines = file($filepath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    return array_reverse(array_slice($lines, -100)); // آخر 100 سطر
}

// حساب الإحصائيات
function calculateStats() {
    $activity_log = __DIR__ . '/logs/activity.log';
    if (!file_exists($activity_log)) {
        return [
            'total_attempts' => 0,
            'successful_generations' => 0,
            'failed_generations' => 0,
            'success_rate' => 0
        ];
    }
    
    $lines = file($activity_log, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    $total_attempts = 0;
    $successful = 0;
    $failed = 0;
    
    foreach ($lines as $line) {
        if (strpos($line, 'APK_GENERATION_START') !== false) {
            $total_attempts++;
        } elseif (strpos($line, 'APK_GENERATION_SUCCESS') !== false) {
            $successful++;
        } elseif (strpos($line, 'APK_GENERATION_ERROR') !== false) {
            $failed++;
        }
    }
    
    $success_rate = $total_attempts > 0 ? ($successful / $total_attempts) * 100 : 0;
    
    return [
        'total_attempts' => $total_attempts,
        'successful_generations' => $successful,
        'failed_generations' => $failed,
        'success_rate' => round($success_rate, 2)
    ];
}

// حساب حجم المجلدات
function getFolderSize($dir) {
    if (!is_dir($dir)) return 0;
    
    $size = 0;
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($files as $file) {
        $size += $file->getSize();
    }
    
    return $size;
}

function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

$stats = calculateStats();
$activity_logs = readLogFile('activity.log');
$error_logs = readLogFile('error.log');
$php_errors = readLogFile('php_errors.log');

$temp_size = getFolderSize(TEMP_DIR);
$downloads_size = getFolderSize(DOWNLOADS_DIR);
$total_size = $temp_size + $downloads_size;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات المشروع</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .log-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .log-content {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.8rem;
            direction: ltr;
            text-align: left;
        }
        
        .success-rate {
            color: #4caf50;
        }
        
        .error-count {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="stats-container">
        <header>
            <h1><i class="fas fa-chart-bar"></i> إحصائيات المشروع</h1>
            <p>لوحة تحكم ومراقبة أداء محول الروابط إلى تطبيقات APK</p>
        </header>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= $stats['total_attempts'] ?></div>
                <div class="stat-label">إجمالي المحاولات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number success-rate"><?= $stats['successful_generations'] ?></div>
                <div class="stat-label">تطبيقات ناجحة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number error-count"><?= $stats['failed_generations'] ?></div>
                <div class="stat-label">محاولات فاشلة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number success-rate"><?= $stats['success_rate'] ?>%</div>
                <div class="stat-label">معدل النجاح</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?= formatBytes($total_size) ?></div>
                <div class="stat-label">مساحة التخزين المستخدمة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?= count(glob(DOWNLOADS_DIR . '/*.apk')) ?></div>
                <div class="stat-label">ملفات APK المتاحة</div>
            </div>
        </div>

        <div class="log-section">
            <h2><i class="fas fa-list"></i> سجل الأنشطة</h2>
            <div class="log-content">
                <?php if (empty($activity_logs)): ?>
                    <p>لا توجد أنشطة مسجلة</p>
                <?php else: ?>
                    <?php foreach ($activity_logs as $log): ?>
                        <div><?= htmlspecialchars($log) ?></div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="log-section">
            <h2><i class="fas fa-exclamation-triangle"></i> سجل الأخطاء</h2>
            <div class="log-content">
                <?php if (empty($error_logs)): ?>
                    <p>لا توجد أخطاء مسجلة</p>
                <?php else: ?>
                    <?php foreach ($error_logs as $log): ?>
                        <div style="color: #f44336;"><?= htmlspecialchars($log) ?></div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <?php if (!empty($php_errors)): ?>
        <div class="log-section">
            <h2><i class="fas fa-bug"></i> أخطاء PHP</h2>
            <div class="log-content">
                <?php foreach ($php_errors as $log): ?>
                    <div style="color: #ff5722;"><?= htmlspecialchars($log) ?></div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="log-section">
            <h2><i class="fas fa-info-circle"></i> معلومات النظام</h2>
            <div class="log-content">
                <div>PHP Version: <?= PHP_VERSION ?></div>
                <div>Server: <?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></div>
                <div>Memory Limit: <?= ini_get('memory_limit') ?></div>
                <div>Upload Max Size: <?= ini_get('upload_max_filesize') ?></div>
                <div>Post Max Size: <?= ini_get('post_max_size') ?></div>
                <div>Max Execution Time: <?= ini_get('max_execution_time') ?> seconds</div>
                <div>GD Extension: <?= extension_loaded('gd') ? 'Enabled' : 'Disabled' ?></div>
                <div>ZIP Extension: <?= extension_loaded('zip') ? 'Enabled' : 'Disabled' ?></div>
                <div>Temp Directory: <?= TEMP_DIR ?> (<?= formatBytes($temp_size) ?>)</div>
                <div>Downloads Directory: <?= DOWNLOADS_DIR ?> (<?= formatBytes($downloads_size) ?>)</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" class="control-btn">
                <i class="fas fa-home"></i> العودة للرئيسية
            </a>
            <button onclick="location.reload()" class="control-btn">
                <i class="fas fa-sync-alt"></i> تحديث
            </button>
        </div>
    </div>
</body>
</html>
