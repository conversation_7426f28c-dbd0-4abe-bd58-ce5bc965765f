<?php
/**
 * اختبار حالة الخادم
 * Server Status Test
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

$tests = [];
$overall_status = true;

// اختبار 1: معلومات PHP
$tests['php_info'] = [
    'name' => 'معلومات PHP',
    'status' => true,
    'data' => [
        'version' => PHP_VERSION,
        'sapi' => php_sapi_name(),
        'os' => PHP_OS,
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size')
    ]
];

// اختبار 2: المكتبات المطلوبة
$required_extensions = ['json', 'gd', 'zip'];
$extensions_status = [];
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    $extensions_status[$ext] = $loaded;
    if (!$loaded) $overall_status = false;
}

$tests['extensions'] = [
    'name' => 'المكتبات المطلوبة',
    'status' => $overall_status,
    'data' => $extensions_status
];

// اختبار 3: المجلدات
$directories = [
    'temp' => __DIR__ . '/temp',
    'downloads' => __DIR__ . '/downloads',
    'logs' => __DIR__ . '/logs'
];

$dir_status = [];
foreach ($directories as $name => $path) {
    $exists = is_dir($path);
    $writable = $exists && is_writable($path);
    
    $dir_status[$name] = [
        'path' => $path,
        'exists' => $exists,
        'writable' => $writable,
        'status' => $exists && $writable
    ];
    
    if (!$exists || !$writable) {
        $overall_status = false;
    }
}

$tests['directories'] = [
    'name' => 'المجلدات المطلوبة',
    'status' => $overall_status,
    'data' => $dir_status
];

// اختبار 4: إنشاء ملف تجريبي
$file_test_status = false;
$file_test_error = '';
try {
    $test_file = __DIR__ . '/temp/test_' . time() . '.txt';
    $test_content = 'اختبار إنشاء ملف - ' . date('Y-m-d H:i:s');
    
    if (file_put_contents($test_file, $test_content) !== false) {
        if (file_exists($test_file) && filesize($test_file) > 0) {
            $file_test_status = true;
            unlink($test_file); // حذف الملف التجريبي
        } else {
            $file_test_error = 'الملف تم إنشاؤه لكنه فارغ';
        }
    } else {
        $file_test_error = 'فشل في إنشاء الملف';
    }
} catch (Exception $e) {
    $file_test_error = $e->getMessage();
}

$tests['file_creation'] = [
    'name' => 'إنشاء الملفات',
    'status' => $file_test_status,
    'data' => [
        'success' => $file_test_status,
        'error' => $file_test_error
    ]
];

if (!$file_test_status) $overall_status = false;

// اختبار 5: معالجة POST
$post_test_status = false;
$post_data = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $post_test_status = true;
    $post_data = [
        'method' => $_SERVER['REQUEST_METHOD'],
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'غير محدد',
        'content_length' => $_SERVER['CONTENT_LENGTH'] ?? 0,
        'post_data' => $_POST,
        'files_data' => $_FILES
    ];
}

$tests['post_handling'] = [
    'name' => 'معالجة POST',
    'status' => $post_test_status,
    'data' => $post_data
];

// اختبار 6: معلومات الخادم
$tests['server_info'] = [
    'name' => 'معلومات الخادم',
    'status' => true,
    'data' => [
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'غير محدد',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'غير محدد',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'غير محدد',
        'http_host' => $_SERVER['HTTP_HOST'] ?? 'غير محدد',
        'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'غير محدد',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'غير محدد'
    ]
];

// النتيجة النهائية
$result = [
    'overall_status' => $overall_status,
    'timestamp' => date('Y-m-d H:i:s'),
    'tests' => $tests,
    'summary' => [
        'total_tests' => count($tests),
        'passed_tests' => count(array_filter($tests, function($test) { return $test['status']; })),
        'failed_tests' => count(array_filter($tests, function($test) { return !$test['status']; }))
    ]
];

// إرجاع النتيجة
echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
