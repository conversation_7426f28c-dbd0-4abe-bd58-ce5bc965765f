<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط لإنشاء APK</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #1976D2;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #e8f5e8;
            color: #4caf50;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #f44336;
            border: 1px solid #f44336;
        }
        .loading {
            background: #e3f2fd;
            color: #2196F3;
            border: 1px solid #2196F3;
        }
        .download-btn {
            background: #4caf50;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            display: inline-block;
            margin-top: 10px;
        }
        .debug-info {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار بسيط لإنشاء APK</h1>
        <p>هذا اختبار مبسط للتحقق من عمل النظام</p>

        <form id="testForm">
            <div class="form-group">
                <label for="website_url">رابط الموقع:</label>
                <input type="url" id="website_url" name="website_url" 
                       value="https://www.google.com" required>
            </div>

            <div class="form-group">
                <label for="app_name">اسم التطبيق:</label>
                <input type="text" id="app_name" name="app_name" 
                       value="تطبيق تجريبي" required>
            </div>

            <button type="submit">إنشاء APK (اختبار)</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            const formData = new FormData(this);
            
            // إظهار رسالة التحميل
            resultDiv.className = 'result loading';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '⏳ جاري إنشاء التطبيق...';
            
            try {
                console.log('إرسال البيانات...');
                
                const response = await fetch('simple_generate.php', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('استجابة الخادم:', response.status, response.statusText);
                
                const responseText = await response.text();
                console.log('محتوى الاستجابة:', responseText);
                
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error('خطأ في تحليل استجابة الخادم: ' + responseText);
                }
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        ✅ تم إنشاء التطبيق بنجاح!<br>
                        <strong>اسم الملف:</strong> ${result.filename}<br>
                        <strong>الحجم:</strong> ${formatBytes(result.file_size)}<br>
                        <strong>اسم الحزمة:</strong> ${result.package_name}<br>
                        <a href="${result.download_url}" class="download-btn" download>
                            📥 تحميل APK
                        </a>
                        <div class="debug-info">معلومات تشخيصية:
${JSON.stringify(result, null, 2)}</div>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        ❌ فشل في إنشاء التطبيق<br>
                        <strong>الخطأ:</strong> ${result.message}<br>
                        <div class="debug-info">معلومات الخطأ:
${JSON.stringify(result, null, 2)}</div>
                    `;
                }
                
            } catch (error) {
                console.error('خطأ في الطلب:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    ❌ حدث خطأ في الاتصال<br>
                    <strong>التفاصيل:</strong> ${error.message}<br>
                    <div class="debug-info">معلومات الخطأ:
${error.stack || error.toString()}</div>
                `;
            }
        });
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
