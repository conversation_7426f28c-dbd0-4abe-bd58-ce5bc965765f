<?php
/**
 * فحص متطلبات النظام
 * System Requirements Checker
 */

$checks = [];
$overall_status = true;

// فحص إصدار PHP
$php_version = PHP_VERSION;
$php_required = '7.4.0';
$php_ok = version_compare($php_version, $php_required, '>=');
$checks['php_version'] = [
    'name' => 'إصدار PHP',
    'status' => $php_ok,
    'current' => $php_version,
    'required' => $php_required . ' أو أحدث',
    'critical' => true
];
if (!$php_ok) $overall_status = false;

// فحص مكتبة GD
$gd_loaded = extension_loaded('gd');
$checks['gd_extension'] = [
    'name' => 'مكتبة GD',
    'status' => $gd_loaded,
    'current' => $gd_loaded ? 'مثبتة' : 'غير مثبتة',
    'required' => 'مطلوبة لمعالجة الصور',
    'critical' => false
];

// فحص مكتبة ZIP
$zip_loaded = extension_loaded('zip');
$checks['zip_extension'] = [
    'name' => 'مكتبة ZIP',
    'status' => $zip_loaded,
    'current' => $zip_loaded ? 'مثبتة' : 'غير مثبتة',
    'required' => 'مطلوبة لإنشاء ملفات APK',
    'critical' => true
];
if (!$zip_loaded) $overall_status = false;

// فحص رفع الملفات
$file_uploads = ini_get('file_uploads');
$checks['file_uploads'] = [
    'name' => 'رفع الملفات',
    'status' => $file_uploads,
    'current' => $file_uploads ? 'مفعل' : 'معطل',
    'required' => 'مطلوب لرفع الأيقونات',
    'critical' => true
];
if (!$file_uploads) $overall_status = false;

// فحص حد رفع الملفات
$upload_max = ini_get('upload_max_filesize');
$upload_bytes = return_bytes($upload_max);
$upload_ok = $upload_bytes >= (5 * 1024 * 1024); // 5MB
$checks['upload_max_filesize'] = [
    'name' => 'حد رفع الملفات',
    'status' => $upload_ok,
    'current' => $upload_max,
    'required' => '5M أو أكثر',
    'critical' => false
];

// فحص حد POST
$post_max = ini_get('post_max_size');
$post_bytes = return_bytes($post_max);
$post_ok = $post_bytes >= (10 * 1024 * 1024); // 10MB
$checks['post_max_size'] = [
    'name' => 'حد POST',
    'status' => $post_ok,
    'current' => $post_max,
    'required' => '10M أو أكثر',
    'critical' => false
];

// فحص وقت التنفيذ
$max_execution = ini_get('max_execution_time');
$execution_ok = $max_execution == 0 || $max_execution >= 300; // 5 دقائق
$checks['max_execution_time'] = [
    'name' => 'وقت التنفيذ الأقصى',
    'status' => $execution_ok,
    'current' => $max_execution == 0 ? 'غير محدود' : $max_execution . ' ثانية',
    'required' => '300 ثانية أو أكثر',
    'critical' => false
];

// فحص الذاكرة
$memory_limit = ini_get('memory_limit');
$memory_bytes = return_bytes($memory_limit);
$memory_ok = $memory_limit == -1 || $memory_bytes >= (128 * 1024 * 1024); // 128MB
$checks['memory_limit'] = [
    'name' => 'حد الذاكرة',
    'status' => $memory_ok,
    'current' => $memory_limit == -1 ? 'غير محدود' : $memory_limit,
    'required' => '128M أو أكثر',
    'critical' => false
];

// فحص المجلدات
$directories = [
    'temp' => __DIR__ . '/temp',
    'downloads' => __DIR__ . '/downloads',
    'logs' => __DIR__ . '/logs'
];

foreach ($directories as $name => $path) {
    $exists = is_dir($path);
    $writable = $exists && is_writable($path);
    
    $checks["dir_$name"] = [
        'name' => "مجلد $name",
        'status' => $exists && $writable,
        'current' => $exists ? ($writable ? 'موجود وقابل للكتابة' : 'موجود لكن غير قابل للكتابة') : 'غير موجود',
        'required' => 'موجود وقابل للكتابة',
        'critical' => true
    ];
    
    if (!$exists || !$writable) $overall_status = false;
}

// دالة تحويل حجم الذاكرة
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    
    return $val;
}

// إذا كان الطلب JSON
if (isset($_GET['json'])) {
    header('Content-Type: application/json');
    echo json_encode([
        'overall_status' => $overall_status,
        'checks' => $checks
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص متطلبات النظام</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .check-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .status-overview {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .status-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .status-ok {
            color: #4caf50;
        }
        
        .status-error {
            color: #f44336;
        }
        
        .checks-list {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .check-item {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .check-item:last-child {
            border-bottom: none;
        }
        
        .check-info {
            flex: 1;
        }
        
        .check-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .check-details {
            font-size: 0.9rem;
            color: #666;
        }
        
        .check-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .badge-ok {
            background: #e8f5e8;
            color: #4caf50;
        }
        
        .badge-error {
            background: #ffebee;
            color: #f44336;
        }
        
        .badge-warning {
            background: #fff3e0;
            color: #ff9800;
        }
        
        .critical-badge {
            background: #f44336;
            color: white;
            font-size: 0.7rem;
            padding: 2px 8px;
            border-radius: 10px;
            margin-right: 10px;
        }
        
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .action-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        
        .action-btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="check-container">
        <header>
            <h1><i class="fas fa-stethoscope"></i> فحص متطلبات النظام</h1>
            <p>التحقق من جاهزية النظام لتشغيل محول الروابط إلى تطبيقات APK</p>
        </header>

        <div class="status-overview">
            <?php if ($overall_status): ?>
                <div class="status-icon status-ok">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2 style="color: #4caf50;">النظام جاهز للتشغيل!</h2>
                <p>جميع المتطلبات الأساسية متوفرة</p>
            <?php else: ?>
                <div class="status-icon status-error">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2 style="color: #f44336;">يحتاج النظام إلى إعداد</h2>
                <p>بعض المتطلبات الأساسية غير متوفرة</p>
            <?php endif; ?>
        </div>

        <div class="checks-list">
            <div style="padding: 20px; background: #f5f5f5; border-bottom: 1px solid #eee;">
                <h3><i class="fas fa-list-check"></i> تفاصيل الفحص</h3>
            </div>
            
            <?php foreach ($checks as $key => $check): ?>
            <div class="check-item">
                <div class="check-info">
                    <div class="check-name">
                        <?= htmlspecialchars($check['name']) ?>
                        <?php if ($check['critical'] && !$check['status']): ?>
                            <span class="critical-badge">مطلوب</span>
                        <?php endif; ?>
                    </div>
                    <div class="check-details">
                        الحالي: <?= htmlspecialchars($check['current']) ?><br>
                        المطلوب: <?= htmlspecialchars($check['required']) ?>
                    </div>
                </div>
                <div class="check-status">
                    <?php if ($check['status']): ?>
                        <i class="fas fa-check-circle status-ok"></i>
                        <span class="status-badge badge-ok">جاهز</span>
                    <?php else: ?>
                        <i class="fas fa-times-circle status-error"></i>
                        <span class="status-badge <?= $check['critical'] ? 'badge-error' : 'badge-warning' ?>">
                            <?= $check['critical'] ? 'خطأ' : 'تحذير' ?>
                        </span>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="actions">
            <a href="index.html" class="action-btn">
                <i class="fas fa-home"></i> الصفحة الرئيسية
            </a>
            <button onclick="location.reload()" class="action-btn">
                <i class="fas fa-sync-alt"></i> إعادة الفحص
            </button>
            <?php if ($overall_status): ?>
            <a href="stats.php?pass=admin123" class="action-btn">
                <i class="fas fa-chart-bar"></i> الإحصائيات
            </a>
            <?php endif; ?>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h3><i class="fas fa-info-circle"></i> معلومات إضافية</h3>
            <p><strong>نظام التشغيل:</strong> <?= PHP_OS ?></p>
            <p><strong>خادم الويب:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف' ?></p>
            <p><strong>مجلد المشروع:</strong> <?= __DIR__ ?></p>
            <p><strong>وقت الفحص:</strong> <?= date('Y-m-d H:i:s') ?></p>
        </div>
    </div>
</body>
</html>
