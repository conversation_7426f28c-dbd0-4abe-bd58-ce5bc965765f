/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* الرأس */
header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* حاوية النموذج */
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

/* الأقسام */
.section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    background: #f9f9f9;
}

.section h2 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

/* مجموعات الإدخال */
.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* رفع الأيقونة */
.icon-upload {
    text-align: center;
}

.icon-upload input[type="file"] {
    display: none;
}

.upload-label {
    display: inline-block;
    padding: 20px 40px;
    background: #667eea;
    color: white;
    border-radius: 10px;
    cursor: pointer;
    transition: background 0.3s ease;
    border: 2px dashed transparent;
}

.upload-label:hover {
    background: #5a6fd8;
}

.upload-label i {
    font-size: 2rem;
    display: block;
    margin-bottom: 10px;
}

.upload-label small {
    display: block;
    margin-top: 5px;
    opacity: 0.8;
}

/* معاينة الأيقونة */
#icon-preview {
    margin-top: 20px;
}

#icon-preview img {
    max-width: 100px;
    max-height: 100px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* أزرار */
.button-group {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.generate-btn, .preview-btn, .test-btn {
    flex: 1;
    min-width: 200px;
    padding: 15px;
    border: none;
    border-radius: 10px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.generate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.preview-btn {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    color: white;
}

.test-btn {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    font-size: 0.9rem;
    min-width: 150px;
    padding: 10px 20px;
}

.generate-btn:hover, .preview-btn:hover, .test-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.generate-btn i, .preview-btn i {
    margin-left: 10px;
}

@media (max-width: 768px) {
    .button-group {
        flex-direction: column;
    }

    .generate-btn, .preview-btn {
        width: 100%;
        min-width: auto;
    }
}

/* منطقة النتائج */
.result-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.result-section h2 {
    color: #4caf50;
    margin-bottom: 20px;
}

.download-btn {
    display: inline-block;
    padding: 15px 30px;
    background: #4caf50;
    color: white;
    text-decoration: none;
    border-radius: 10px;
    font-weight: 600;
    transition: background 0.3s ease;
}

.download-btn:hover {
    background: #45a049;
}

/* منطقة التحميل */
.loading-section {
    background: white;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* التذييل */
footer {
    text-align: center;
    color: white;
    padding: 20px;
    opacity: 0.8;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .form-container {
        padding: 20px;
    }
    
    .section {
        padding: 15px;
    }
}

/* تحسينات إضافية */
.input-group input[type="checkbox"] {
    width: auto;
    margin-left: 10px;
}

.input-group input[type="color"] {
    width: 60px;
    height: 40px;
    padding: 0;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

/* زر تخصيص اسم الحزمة */
.edit-package-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    cursor: pointer;
    margin-right: 10px;
    transition: background 0.3s;
}

.edit-package-btn:hover {
    background: #5a6fd8;
}

.input-group input[readonly] {
    background-color: #f5f5f5;
    color: #666;
}

.input-group input[readonly]:focus {
    border-color: #ddd;
    box-shadow: none;
}

/* تأثيرات الانتقال */
.section {
    transition: transform 0.3s ease;
}

.section:hover {
    transform: translateY(-2px);
}

/* تحسين مظهر النموذج */
.form-container {
    position: relative;
    overflow: hidden;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}
