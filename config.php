<?php
/**
 * ملف إعدادات المشروع
 * Web to APK Converter Configuration
 */

// إعدادات عامة
define('APP_NAME', 'محول الروابط إلى تطبيقات APK');
define('APP_VERSION', '1.0.0');
define('APP_AUTHOR', 'مطور عربي');

// إعدادات المجلدات
define('TEMP_DIR', __DIR__ . '/temp');
define('DOWNLOADS_DIR', __DIR__ . '/downloads');
define('UPLOADS_DIR', __DIR__ . '/uploads');

// إعدادات الملفات
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['image/png', 'image/jpeg', 'image/jpg']);
define('MAX_EXECUTION_TIME', 300); // 5 دقائق

// إعدادات APK
define('DEFAULT_PACKAGE_PREFIX', 'com.webtoapp');
define('DEFAULT_MIN_SDK', 21);
define('DEFAULT_TARGET_SDK', 33);
define('DEFAULT_COMPILE_SDK', 33);

// إعدادات الأمان
define('ENABLE_DEBUG', false);
define('LOG_ERRORS', true);
define('CLEANUP_TEMP_FILES', true);

// إعدادات قاعدة البيانات (للمستقبل)
define('DB_HOST', 'localhost');
define('DB_NAME', 'webtoapp');
define('DB_USER', 'root');
define('DB_PASS', '');

// إعدادات البريد الإلكتروني (للمستقبل)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USER', '');
define('SMTP_PASS', '');

// إعدادات التخزين السحابي (للمستقبل)
define('CLOUD_STORAGE_ENABLED', false);
define('CLOUD_STORAGE_PROVIDER', 'aws'); // aws, google, azure

// دالة للحصول على إعدادات APK الافتراضية
function getDefaultAPKSettings() {
    return [
        'min_sdk' => DEFAULT_MIN_SDK,
        'target_sdk' => DEFAULT_TARGET_SDK,
        'compile_sdk' => DEFAULT_COMPILE_SDK,
        'version_code' => 1,
        'version_name' => '1.0',
        'orientation' => 'portrait',
        'theme_color' => '#2196F3',
        'fullscreen' => false,
        'splash_screen' => true,
        'allow_zoom' => true,
        'user_agent' => 'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36'
    ];
}

// دالة للحصول على إعدادات الأمان
function getSecuritySettings() {
    return [
        'max_file_size' => MAX_FILE_SIZE,
        'allowed_image_types' => ALLOWED_IMAGE_TYPES,
        'max_execution_time' => MAX_EXECUTION_TIME,
        'enable_debug' => ENABLE_DEBUG,
        'log_errors' => LOG_ERRORS
    ];
}

// دالة تهيئة المجلدات
function initializeDirectories() {
    $directories = [
        TEMP_DIR,
        DOWNLOADS_DIR,
        UPLOADS_DIR,
        __DIR__ . '/logs'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new Exception("فشل في إنشاء المجلد: $dir");
            }
        }
        
        // التأكد من أن المجلد قابل للكتابة
        if (!is_writable($dir)) {
            throw new Exception("المجلد غير قابل للكتابة: $dir");
        }
    }
}

// دالة تسجيل الأخطاء
function logError($message, $file = null, $line = null) {
    if (!LOG_ERRORS) return;
    
    $log_file = __DIR__ . '/logs/error.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message";
    
    if ($file && $line) {
        $log_message .= " في $file:$line";
    }
    
    $log_message .= PHP_EOL;
    
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
}

// دالة تسجيل العمليات
function logActivity($action, $details = '') {
    $log_file = __DIR__ . '/logs/activity.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    $log_message = "[$timestamp] IP: $ip | Action: $action | Details: $details | User-Agent: $user_agent" . PHP_EOL;
    
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
}

// دالة تنظيف الملفات القديمة
function cleanupOldFiles() {
    if (!CLEANUP_TEMP_FILES) return;
    
    $directories = [TEMP_DIR, DOWNLOADS_DIR];
    $max_age = 24 * 60 * 60; // 24 ساعة
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) continue;
        
        $files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($files as $file) {
            if (time() - $file->getMTime() > $max_age) {
                if ($file->isDir()) {
                    @rmdir($file->getRealPath());
                } else {
                    @unlink($file->getRealPath());
                }
            }
        }
    }
}

// تهيئة المشروع
try {
    initializeDirectories();
    
    // تنظيف الملفات القديمة (يتم تشغيله أحياناً فقط)
    if (rand(1, 100) <= 5) { // 5% احتمال
        cleanupOldFiles();
    }
    
} catch (Exception $e) {
    logError("خطأ في تهيئة المشروع: " . $e->getMessage());
    if (ENABLE_DEBUG) {
        die("خطأ في تهيئة المشروع: " . $e->getMessage());
    }
}

// إعدادات PHP
ini_set('upload_max_filesize', '10M');
ini_set('post_max_size', '15M');
ini_set('max_execution_time', MAX_EXECUTION_TIME);
ini_set('memory_limit', '256M');

if (LOG_ERRORS) {
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/logs/php_errors.log');
}

if (!ENABLE_DEBUG) {
    ini_set('display_errors', 0);
    error_reporting(0);
}
?>
