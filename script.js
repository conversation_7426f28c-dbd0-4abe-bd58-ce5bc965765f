// متغيرات عامة
let selectedIcon = null;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeForm();
    setupEventListeners();
});

// إعداد النموذج
function initializeForm() {
    // إنشاء اسم حزمة تلقائي
    const appNameInput = document.getElementById('app_name');
    const packageNameInput = document.getElementById('package_name');

    // دالة تنظيف النص العربي والإنجليزي
    function cleanAppName(name) {
        return name
            .toLowerCase()
            .replace(/[\u0600-\u06FF]/g, '') // إزالة الأحرف العربية
            .replace(/[^a-z0-9]/g, '') // إزالة كل شيء عدا الأحرف الإنجليزية والأرقام
            .substring(0, 15); // تحديد الطول
    }

    appNameInput.addEventListener('input', function() {
        // تحديث اسم الحزمة فقط إذا كان في وضع القراءة فقط (تلقائي)
        if (packageNameInput.readOnly) {
            const cleanName = cleanAppName(this.value);
            if (cleanName) {
                packageNameInput.value = `com.webtoapp.${cleanName}`;
            } else if (this.value.trim()) {
                // إذا لم يكن هناك أحرف إنجليزية، استخدم اسم عام
                const timestamp = Date.now().toString().slice(-6);
                packageNameInput.value = `com.webtoapp.app${timestamp}`;
            } else {
                // إذا كان الحقل فارغ
                packageNameInput.value = '';
            }

            // إضافة تأثير بصري لإظهار التحديث
            packageNameInput.style.transition = 'background-color 0.3s';
            packageNameInput.style.backgroundColor = '#e8f5e8';
            setTimeout(() => {
                packageNameInput.style.backgroundColor = '#f5f5f5';
            }, 500);
        }
    });

    // إضافة أمثلة متغيرة لاسم التطبيق
    const examples = [
        'متجري الإلكتروني',
        'مطعم الأصالة',
        'عيادة الدكتور أحمد',
        'مكتبة الحكمة',
        'ورشة السيارات',
        'صالون الجمال',
        'مقهى الأصدقاء',
        'شركة البناء'
    ];

    let exampleIndex = 0;
    setInterval(() => {
        if (!appNameInput.value && document.activeElement !== appNameInput) {
            appNameInput.placeholder = examples[exampleIndex];
            exampleIndex = (exampleIndex + 1) % examples.length;
        }
    }, 3000);
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // رفع الأيقونة
    const iconInput = document.getElementById('app_icon');
    iconInput.addEventListener('change', handleIconUpload);
    
    // إرسال النموذج
    const form = document.getElementById('apkForm');
    form.addEventListener('submit', handleFormSubmit);
    
    // التحقق من صحة الرابط
    const urlInput = document.getElementById('website_url');
    urlInput.addEventListener('blur', validateUrl);
}

// التعامل مع رفع الأيقونة
function handleIconUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        showError('يرجى اختيار ملف صورة صحيح');
        return;
    }
    
    // التحقق من حجم الملف (5MB كحد أقصى)
    if (file.size > 5 * 1024 * 1024) {
        showError('حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 5MB');
        return;
    }
    
    selectedIcon = file;
    
    // عرض معاينة الأيقونة
    const reader = new FileReader();
    reader.onload = function(e) {
        const preview = document.getElementById('icon-preview');
        preview.innerHTML = `
            <img src="${e.target.result}" alt="معاينة الأيقونة">
            <p>تم اختيار الأيقونة بنجاح</p>
        `;
    };
    reader.readAsDataURL(file);
}

// التحقق من صحة الرابط
function validateUrl(event) {
    const url = event.target.value;
    if (!url) return;
    
    try {
        new URL(url);
        event.target.style.borderColor = '#4caf50';
    } catch {
        event.target.style.borderColor = '#f44336';
        showError('يرجى إدخال رابط صحيح');
    }
}

// التعامل مع إرسال النموذج
async function handleFormSubmit(event) {
    event.preventDefault();
    
    // التحقق من البيانات المطلوبة
    if (!validateForm()) {
        return;
    }
    
    // إظهار شاشة التحميل
    showLoading();
    
    try {
        // إعداد البيانات للإرسال
        const formData = new FormData();
        
        // إضافة البيانات النصية
        const formElements = event.target.elements;
        for (let element of formElements) {
            if (element.type === 'checkbox') {
                formData.append(element.name, element.checked ? '1' : '0');
            } else if (element.type !== 'file' && element.name) {
                formData.append(element.name, element.value);
            }
        }
        
        // إضافة الأيقونة إذا تم اختيارها
        if (selectedIcon) {
            formData.append('app_icon', selectedIcon);
        }
        
        // إرسال البيانات إلى الخادم
        console.log('إرسال البيانات إلى الخادم...');

        // محاولة استخدام المعالج الأساسي أولاً
        let response;
        try {
            response = await fetch('generate.php', {
                method: 'POST',
                body: formData
            });
        } catch (error) {
            console.log('فشل المعالج الأساسي، جاري المحاولة مع المعالج المبسط...');
            // إذا فشل، جرب المعالج المبسط
            response = await fetch('generate_simple.php', {
                method: 'POST',
                body: formData
            });
        }

        console.log('استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`خطأ في الخادم: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        console.log('نتيجة معالجة الخادم:', result);
        
        if (result.success) {
            console.log('نجح إنشاء التطبيق:', result);
            showSuccessWithDownload(result.download_url, result.filename);
        } else {
            console.error('فشل إنشاء التطبيق:', result);
            showError(result.message || 'حدث خطأ أثناء إنشاء التطبيق');
        }
        
    } catch (error) {
        console.error('خطأ:', error);
        showError('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى');
    } finally {
        hideLoading();
    }
}

// التحقق من صحة النموذج
function validateForm() {
    const requiredFields = ['website_url', 'app_name'];
    
    for (let fieldName of requiredFields) {
        const field = document.getElementById(fieldName);
        if (!field.value.trim()) {
            field.focus();
            showError(`يرجى ملء حقل ${field.previousElementSibling.textContent}`);
            return false;
        }
    }
    
    // التحقق من صحة الرابط
    try {
        new URL(document.getElementById('website_url').value);
    } catch {
        showError('يرجى إدخال رابط صحيح');
        return false;
    }
    
    return true;
}

// إظهار شاشة التحميل
function showLoading() {
    document.getElementById('loading-section').style.display = 'block';
    document.getElementById('result-section').style.display = 'none';
    
    // تمرير إلى شاشة التحميل
    document.getElementById('loading-section').scrollIntoView({
        behavior: 'smooth'
    });
}

// إخفاء شاشة التحميل
function hideLoading() {
    document.getElementById('loading-section').style.display = 'none';
}

// إظهار النجاح مع رابط التحميل
function showSuccessWithDownload(downloadUrl, filename) {
    const resultSection = document.getElementById('result-section');
    const downloadLink = document.getElementById('download-link');

    console.log('إعداد رابط التحميل:', downloadUrl, filename);

    // تحديث رابط التحميل
    downloadLink.href = downloadUrl;
    downloadLink.download = filename;

    // التأكد من أن الرابط يعمل
    downloadLink.onclick = function(e) {
        console.log('تم النقر على رابط التحميل:', this.href);

        // إضافة تحميل تلقائي كبديل
        setTimeout(() => {
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filename;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }, 100);
    };

    // تحديث تفاصيل التطبيق
    const appInfoContent = document.getElementById('app-info-content');
    if (appInfoContent) {
        appInfoContent.innerHTML = `
            <p><strong>اسم التطبيق:</strong> ${result.app_name || 'غير محدد'}</p>
            <p><strong>اسم الحزمة:</strong> ${result.package_name || 'غير محدد'}</p>
            <p><strong>اسم الملف:</strong> ${filename}</p>
            <p><strong>حجم الملف:</strong> ${result.file_size ? formatBytes(result.file_size) : 'غير محدد'}</p>
            <p><strong>وقت الإنشاء:</strong> ${result.timestamp || new Date().toLocaleString()}</p>
        `;
    }

    // إظهار منطقة النتائج
    resultSection.style.display = 'block';

    // تمرير إلى منطقة النتائج
    resultSection.scrollIntoView({
        behavior: 'smooth'
    });

    // إضافة تأثير صوتي (اختياري)
    playSuccessSound();

    // إضافة رسالة نجاح مفصلة
    const successMessage = `
        تم إنشاء التطبيق بنجاح! 🎉<br>
        <strong>اسم الملف:</strong> ${filename}<br>
        <strong>الحجم:</strong> ${result.file_size ? formatBytes(result.file_size) : 'غير محدد'}<br>
        يمكنك تحميله الآن من الزر أدناه.
    `;
    showMessage(successMessage, '#4caf50');
}

// إظهار النجاح (للاستخدام العام)
function showSuccess(message) {
    showMessage(message, '#4caf50');
}

// إظهار رسالة خطأ
function showError(message) {
    // إنشاء عنصر الخطأ
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        ${message}
    `;
    
    // إضافة الأنماط
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f44336;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(errorDiv);
    
    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        errorDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 300);
    }, 5000);
}

// تشغيل صوت النجاح (اختياري)
function playSuccessSound() {
    try {
        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
        audio.play().catch(() => {
            // تجاهل الخطأ إذا لم يتمكن من تشغيل الصوت
        });
    } catch (error) {
        // تجاهل الخطأ
    }
}

// دالة تبديل تخصيص اسم الحزمة
function togglePackageEdit() {
    const packageInput = document.getElementById('package_name');
    const editBtn = document.getElementById('edit_package_btn');

    if (packageInput.readOnly) {
        // تفعيل التحرير
        packageInput.readOnly = false;
        packageInput.style.backgroundColor = 'white';
        packageInput.style.color = '#333';
        packageInput.focus();
        editBtn.innerHTML = '<i class="fas fa-save"></i> حفظ';
        editBtn.style.background = '#4caf50';

        showInfo('يمكنك الآن تخصيص اسم الحزمة. تأكد من استخدام تنسيق صحيح مثل: com.company.appname');
    } else {
        // حفظ وإغلاق التحرير
        if (validatePackageName(packageInput.value)) {
            packageInput.readOnly = true;
            packageInput.style.backgroundColor = '#f5f5f5';
            packageInput.style.color = '#666';
            editBtn.innerHTML = '<i class="fas fa-edit"></i> تخصيص';
            editBtn.style.background = '#667eea';

            showSuccess('تم حفظ اسم الحزمة المخصص');
        } else {
            showError('اسم الحزمة غير صحيح. يجب أن يكون بتنسيق: com.company.appname');
        }
    }
}

// دالة التحقق من صحة اسم الحزمة
function validatePackageName(packageName) {
    const packageRegex = /^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)+$/;
    return packageRegex.test(packageName);
}

// دالة عرض رسالة معلومات
function showInfo(message) {
    showMessage(message, '#2196F3');
}

// دالة عرض رسالة نجاح
function showSuccess(message) {
    showMessage(message, '#4caf50');
}

// دالة عامة لعرض الرسائل
function showMessage(message, color) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'info-message';
    messageDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;

    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${color};
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        z-index: 1000;
        animation: slideIn 0.3s ease;
        max-width: 300px;
    `;

    document.body.appendChild(messageDiv);

    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 4000);
}

// دالة تنسيق حجم الملف
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// دالة اختبار التحميل
function testDownload() {
    console.log('اختبار التحميل...');

    // إنشاء رابط تحميل تجريبي
    const link = document.createElement('a');
    link.href = 'test_download.php';
    link.download = 'test_download.txt';
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage('تم بدء اختبار التحميل. إذا لم يبدأ التحميل، فهناك مشكلة في إعدادات المتصفح أو الخادم.', '#ff9800');
}

// دالة معاينة التطبيق
function showPreview() {
    // جمع البيانات من النموذج
    const appName = document.getElementById('app_name').value || 'اسم التطبيق';
    const websiteUrl = document.getElementById('website_url').value || 'https://example.com';
    const themeColor = document.getElementById('theme_color').value || '#2196F3';

    // التحقق من الحقول المطلوبة
    if (!document.getElementById('website_url').value) {
        showError('يرجى إدخال رابط الموقع أولاً');
        return;
    }

    // إنشاء URL للمعاينة
    const previewUrl = new URL('preview.html', window.location.href);
    previewUrl.searchParams.set('app_name', appName);
    previewUrl.searchParams.set('website_url', websiteUrl);
    previewUrl.searchParams.set('theme_color', themeColor);

    // إضافة الأيقونة إذا كانت متاحة
    if (selectedIcon) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewUrl.searchParams.set('app_icon', e.target.result);
            window.open(previewUrl.toString(), '_blank');
        };
        reader.readAsDataURL(selectedIcon);
    } else {
        window.open(previewUrl.toString(), '_blank');
    }
}

// إضافة أنماط CSS للرسائل
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .error-message i {
        margin-left: 10px;
    }
`;
document.head.appendChild(style);
