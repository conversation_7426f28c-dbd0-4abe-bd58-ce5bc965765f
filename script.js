// متغيرات عامة
let selectedIcon = null;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeForm();
    setupEventListeners();
});

// إعداد النموذج
function initializeForm() {
    // إنشاء اسم حزمة تلقائي
    const appNameInput = document.getElementById('app_name');
    const packageNameInput = document.getElementById('package_name');
    
    appNameInput.addEventListener('input', function() {
        if (!packageNameInput.value) {
            const appName = this.value.toLowerCase()
                .replace(/[^a-z0-9]/g, '')
                .substring(0, 20);
            packageNameInput.value = `com.webtoapp.${appName}`;
        }
    });
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // رفع الأيقونة
    const iconInput = document.getElementById('app_icon');
    iconInput.addEventListener('change', handleIconUpload);
    
    // إرسال النموذج
    const form = document.getElementById('apkForm');
    form.addEventListener('submit', handleFormSubmit);
    
    // التحقق من صحة الرابط
    const urlInput = document.getElementById('website_url');
    urlInput.addEventListener('blur', validateUrl);
}

// التعامل مع رفع الأيقونة
function handleIconUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        showError('يرجى اختيار ملف صورة صحيح');
        return;
    }
    
    // التحقق من حجم الملف (5MB كحد أقصى)
    if (file.size > 5 * 1024 * 1024) {
        showError('حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 5MB');
        return;
    }
    
    selectedIcon = file;
    
    // عرض معاينة الأيقونة
    const reader = new FileReader();
    reader.onload = function(e) {
        const preview = document.getElementById('icon-preview');
        preview.innerHTML = `
            <img src="${e.target.result}" alt="معاينة الأيقونة">
            <p>تم اختيار الأيقونة بنجاح</p>
        `;
    };
    reader.readAsDataURL(file);
}

// التحقق من صحة الرابط
function validateUrl(event) {
    const url = event.target.value;
    if (!url) return;
    
    try {
        new URL(url);
        event.target.style.borderColor = '#4caf50';
    } catch {
        event.target.style.borderColor = '#f44336';
        showError('يرجى إدخال رابط صحيح');
    }
}

// التعامل مع إرسال النموذج
async function handleFormSubmit(event) {
    event.preventDefault();
    
    // التحقق من البيانات المطلوبة
    if (!validateForm()) {
        return;
    }
    
    // إظهار شاشة التحميل
    showLoading();
    
    try {
        // إعداد البيانات للإرسال
        const formData = new FormData();
        
        // إضافة البيانات النصية
        const formElements = event.target.elements;
        for (let element of formElements) {
            if (element.type === 'checkbox') {
                formData.append(element.name, element.checked ? '1' : '0');
            } else if (element.type !== 'file' && element.name) {
                formData.append(element.name, element.value);
            }
        }
        
        // إضافة الأيقونة إذا تم اختيارها
        if (selectedIcon) {
            formData.append('app_icon', selectedIcon);
        }
        
        // إرسال البيانات إلى الخادم
        const response = await fetch('generate.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(result.download_url, result.filename);
        } else {
            showError(result.message || 'حدث خطأ أثناء إنشاء التطبيق');
        }
        
    } catch (error) {
        console.error('خطأ:', error);
        showError('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى');
    } finally {
        hideLoading();
    }
}

// التحقق من صحة النموذج
function validateForm() {
    const requiredFields = ['website_url', 'app_name'];
    
    for (let fieldName of requiredFields) {
        const field = document.getElementById(fieldName);
        if (!field.value.trim()) {
            field.focus();
            showError(`يرجى ملء حقل ${field.previousElementSibling.textContent}`);
            return false;
        }
    }
    
    // التحقق من صحة الرابط
    try {
        new URL(document.getElementById('website_url').value);
    } catch {
        showError('يرجى إدخال رابط صحيح');
        return false;
    }
    
    return true;
}

// إظهار شاشة التحميل
function showLoading() {
    document.getElementById('loading-section').style.display = 'block';
    document.getElementById('result-section').style.display = 'none';
    
    // تمرير إلى شاشة التحميل
    document.getElementById('loading-section').scrollIntoView({
        behavior: 'smooth'
    });
}

// إخفاء شاشة التحميل
function hideLoading() {
    document.getElementById('loading-section').style.display = 'none';
}

// إظهار النجاح
function showSuccess(downloadUrl, filename) {
    const resultSection = document.getElementById('result-section');
    const downloadLink = document.getElementById('download-link');
    
    downloadLink.href = downloadUrl;
    downloadLink.download = filename;
    
    resultSection.style.display = 'block';
    
    // تمرير إلى منطقة النتائج
    resultSection.scrollIntoView({
        behavior: 'smooth'
    });
    
    // إضافة تأثير صوتي (اختياري)
    playSuccessSound();
}

// إظهار رسالة خطأ
function showError(message) {
    // إنشاء عنصر الخطأ
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        ${message}
    `;
    
    // إضافة الأنماط
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f44336;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(errorDiv);
    
    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        errorDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 300);
    }, 5000);
}

// تشغيل صوت النجاح (اختياري)
function playSuccessSound() {
    try {
        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
        audio.play().catch(() => {
            // تجاهل الخطأ إذا لم يتمكن من تشغيل الصوت
        });
    } catch (error) {
        // تجاهل الخطأ
    }
}

// دالة معاينة التطبيق
function showPreview() {
    // جمع البيانات من النموذج
    const appName = document.getElementById('app_name').value || 'اسم التطبيق';
    const websiteUrl = document.getElementById('website_url').value || 'https://example.com';
    const themeColor = document.getElementById('theme_color').value || '#2196F3';

    // التحقق من الحقول المطلوبة
    if (!document.getElementById('website_url').value) {
        showError('يرجى إدخال رابط الموقع أولاً');
        return;
    }

    // إنشاء URL للمعاينة
    const previewUrl = new URL('preview.html', window.location.href);
    previewUrl.searchParams.set('app_name', appName);
    previewUrl.searchParams.set('website_url', websiteUrl);
    previewUrl.searchParams.set('theme_color', themeColor);

    // إضافة الأيقونة إذا كانت متاحة
    if (selectedIcon) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewUrl.searchParams.set('app_icon', e.target.result);
            window.open(previewUrl.toString(), '_blank');
        };
        reader.readAsDataURL(selectedIcon);
    } else {
        window.open(previewUrl.toString(), '_blank');
    }
}

// إضافة أنماط CSS للرسائل
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .error-message i {
        margin-left: 10px;
    }
`;
document.head.appendChild(style);
