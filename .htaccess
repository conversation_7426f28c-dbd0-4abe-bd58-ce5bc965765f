# إعدادات Apache للمشروع

# تفعيل إعادة الكتابة
RewriteEngine On

# إعدادات PHP
php_value upload_max_filesize 10M
php_value post_max_size 15M
php_value max_execution_time 300
php_value memory_limit 256M

# إعدادات الأمان
# منع الوصول للملفات الحساسة
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

# منع الوصول لمجلد temp
<Directory "temp">
    Order Deny,Allow
    Deny from all
</Directory>

# السماح بتحميل ملفات APK
<Directory "downloads">
    Order Allow,Deny
    Allow from all
</Directory>

# إعدادات MIME للملفات
AddType application/vnd.android.package-archive .apk

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# إعدادات التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# إعدادات CORS
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>
