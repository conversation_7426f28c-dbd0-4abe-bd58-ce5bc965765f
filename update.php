<?php
/**
 * أداة تحديث المشروع
 * Project Update Tool
 */

require_once 'config.php';

$password = $_POST['pass'] ?? $_GET['pass'] ?? '';
if ($password !== 'admin123') {
    die('غير مصرح لك بالوصول');
}

$action = $_POST['action'] ?? '';
$message = '';
$error = '';

// معلومات الإصدار الحالي
$current_version = '1.0.0';
$update_available = false;

// فحص التحديثات (محاكاة)
function checkForUpdates() {
    // في التطبيق الحقيقي، سيتم فحص خادم التحديثات
    return [
        'available' => false,
        'latest_version' => '1.0.0',
        'release_notes' => 'لا توجد تحديثات متاحة حالياً',
        'download_url' => ''
    ];
}

// تنظيف شامل للمشروع
if ($action === 'deep_clean') {
    try {
        // تنظيف الملفات المؤقتة
        cleanupOldFiles();
        
        // تنظيف ملفات APK القديمة (أكثر من أسبوع)
        $downloads_dir = DOWNLOADS_DIR;
        if (is_dir($downloads_dir)) {
            $files = scandir($downloads_dir);
            $cleaned_count = 0;
            
            foreach ($files as $file) {
                if ($file === '.' || $file === '..') continue;
                
                $filepath = $downloads_dir . '/' . $file;
                if (is_file($filepath) && time() - filemtime($filepath) > (7 * 24 * 60 * 60)) {
                    if (unlink($filepath)) {
                        $cleaned_count++;
                    }
                }
            }
            
            $message = "تم تنظيف $cleaned_count ملف قديم";
        }
        
        logActivity('DEEP_CLEAN_PERFORMED', 'تنظيف شامل للمشروع');
        
    } catch (Exception $e) {
        $error = 'خطأ في التنظيف: ' . $e->getMessage();
        logError($error);
    }
}

// إعادة تعيين الإعدادات
if ($action === 'reset_config') {
    try {
        // إعادة إنشاء المجلدات
        initializeDirectories();
        
        // مسح السجلات
        $log_files = ['activity.log', 'error.log', 'php_errors.log'];
        foreach ($log_files as $log_file) {
            $filepath = __DIR__ . '/logs/' . $log_file;
            if (file_exists($filepath)) {
                file_put_contents($filepath, '');
            }
        }
        
        $message = 'تم إعادة تعيين الإعدادات بنجاح';
        logActivity('CONFIG_RESET', 'إعادة تعيين الإعدادات');
        
    } catch (Exception $e) {
        $error = 'خطأ في إعادة التعيين: ' . $e->getMessage();
        logError($error);
    }
}

// إصلاح الأذونات
if ($action === 'fix_permissions') {
    try {
        $directories = [TEMP_DIR, DOWNLOADS_DIR, __DIR__ . '/logs'];
        $fixed_count = 0;
        
        foreach ($directories as $dir) {
            if (is_dir($dir)) {
                if (chmod($dir, 0755)) {
                    $fixed_count++;
                }
            }
        }
        
        $message = "تم إصلاح أذونات $fixed_count مجلد";
        logActivity('PERMISSIONS_FIXED', 'إصلاح الأذونات');
        
    } catch (Exception $e) {
        $error = 'خطأ في إصلاح الأذونات: ' . $e->getMessage();
        logError($error);
    }
}

$update_info = checkForUpdates();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث المشروع</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .update-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .update-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .version-info {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .version-badge {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .update-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .action-card {
            background: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s;
        }
        
        .action-card:hover {
            transform: translateY(-2px);
        }
        
        .action-icon {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .action-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
            width: 100%;
        }
        
        .action-btn:hover {
            background: #5a6fd8;
        }
        
        .action-btn.danger {
            background: #f44336;
        }
        
        .action-btn.danger:hover {
            background: #d32f2f;
        }
        
        .message {
            background: #e8f5e8;
            color: #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .error {
            background: #ffebee;
            color: #f44336;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .system-info {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        
        .info-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .info-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="update-container">
        <header>
            <h1><i class="fas fa-sync-alt"></i> تحديث المشروع</h1>
            <p>أدوات صيانة وتحديث محول الروابط إلى تطبيقات APK</p>
        </header>

        <?php if ($message): ?>
        <div class="message">
            <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="error">
            <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
        </div>
        <?php endif; ?>

        <div class="update-card">
            <div class="version-info">
                <h2>الإصدار الحالي</h2>
                <div class="version-badge">v<?= $current_version ?></div>
                <p style="margin-top: 15px; color: #666;">
                    آخر تحديث: <?= date('Y-m-d') ?>
                </p>
            </div>

            <?php if ($update_info['available']): ?>
            <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center;">
                <h3 style="color: #1976d2;">
                    <i class="fas fa-download"></i> تحديث متاح!
                </h3>
                <p>الإصدار الجديد: v<?= $update_info['latest_version'] ?></p>
                <p><?= htmlspecialchars($update_info['release_notes']) ?></p>
                <button class="action-btn" style="margin-top: 15px;">
                    <i class="fas fa-download"></i> تحميل التحديث
                </button>
            </div>
            <?php else: ?>
            <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center;">
                <h3 style="color: #4caf50;">
                    <i class="fas fa-check-circle"></i> المشروع محدث
                </h3>
                <p>لديك أحدث إصدار من المشروع</p>
            </div>
            <?php endif; ?>
        </div>

        <div class="update-card">
            <h2><i class="fas fa-tools"></i> أدوات الصيانة</h2>
            
            <div class="update-actions">
                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-broom"></i>
                    </div>
                    <h3>تنظيف شامل</h3>
                    <p>حذف الملفات المؤقتة والقديمة</p>
                    <form method="post" style="margin-top: 15px;">
                        <input type="hidden" name="pass" value="<?= htmlspecialchars($password) ?>">
                        <button type="submit" name="action" value="deep_clean" class="action-btn"
                                onclick="return confirm('هل أنت متأكد من التنظيف الشامل؟')">
                            تنظيف الآن
                        </button>
                    </form>
                </div>

                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <h3>إعادة تعيين</h3>
                    <p>إعادة تعيين الإعدادات والسجلات</p>
                    <form method="post" style="margin-top: 15px;">
                        <input type="hidden" name="pass" value="<?= htmlspecialchars($password) ?>">
                        <button type="submit" name="action" value="reset_config" class="action-btn danger"
                                onclick="return confirm('هل أنت متأكد من إعادة التعيين؟ سيتم مسح جميع السجلات!')">
                            إعادة تعيين
                        </button>
                    </form>
                </div>

                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <h3>إصلاح الأذونات</h3>
                    <p>إصلاح أذونات المجلدات</p>
                    <form method="post" style="margin-top: 15px;">
                        <input type="hidden" name="pass" value="<?= htmlspecialchars($password) ?>">
                        <button type="submit" name="action" value="fix_permissions" class="action-btn">
                            إصلاح الأذونات
                        </button>
                    </form>
                </div>

                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-stethoscope"></i>
                    </div>
                    <h3>فحص النظام</h3>
                    <p>فحص شامل لمتطلبات النظام</p>
                    <a href="check.php" class="action-btn" style="display: block; text-decoration: none; color: white;">
                        فحص الآن
                    </a>
                </div>
            </div>
        </div>

        <div class="update-card">
            <h2><i class="fas fa-info-circle"></i> معلومات النظام</h2>
            
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-value"><?= PHP_VERSION ?></div>
                    <div class="info-label">إصدار PHP</div>
                </div>
                
                <div class="info-item">
                    <div class="info-value"><?= formatBytes(getFolderSize(DOWNLOADS_DIR)) ?></div>
                    <div class="info-label">ملفات التحميل</div>
                </div>
                
                <div class="info-item">
                    <div class="info-value"><?= formatBytes(getFolderSize(TEMP_DIR)) ?></div>
                    <div class="info-label">ملفات مؤقتة</div>
                </div>
                
                <div class="info-item">
                    <div class="info-value"><?= count(glob(DOWNLOADS_DIR . '/*.apk')) ?></div>
                    <div class="info-label">ملفات APK</div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" class="action-btn">
                <i class="fas fa-home"></i> الرئيسية
            </a>
            <a href="stats.php?pass=<?= urlencode($password) ?>" class="action-btn">
                <i class="fas fa-chart-bar"></i> الإحصائيات
            </a>
            <a href="manage.php?pass=<?= urlencode($password) ?>" class="action-btn">
                <i class="fas fa-cog"></i> الإدارة
            </a>
        </div>
    </div>
</body>
</html>
