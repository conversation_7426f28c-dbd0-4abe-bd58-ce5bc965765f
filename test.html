<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إنشاء اسم الحزمة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .test-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        .result {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-right: 4px solid #4caf50;
        }
        .examples {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار إنشاء اسم الحزمة التلقائي</h1>
        
        <div class="examples">
            <h3>أمثلة للاختبار:</h3>
            <ul>
                <li>متجر الكتب</li>
                <li>مطعم الأصالة</li>
                <li>عيادة الدكتور أحمد</li>
                <li>My Store</li>
                <li>مقهى الأصدقاء 123</li>
                <li>صالون الجمال</li>
            </ul>
        </div>
        
        <label for="app_name">اسم التطبيق:</label>
        <input type="text" id="app_name" class="test-input" placeholder="أدخل اسم التطبيق هنا...">
        
        <div class="result" id="result" style="display: none;">
            <strong>اسم الحزمة المُنشأ:</strong>
            <div id="package_name" style="font-family: monospace; font-size: 18px; color: #2196F3; margin-top: 10px;"></div>
        </div>
    </div>

    <script>
        // دالة تنظيف النص العربي والإنجليزي (نفس الدالة في المشروع)
        function cleanAppName(name) {
            return name
                .toLowerCase()
                .replace(/[\u0600-\u06FF]/g, '') // إزالة الأحرف العربية
                .replace(/[^a-z0-9]/g, '') // إزالة كل شيء عدا الأحرف الإنجليزية والأرقام
                .substring(0, 15); // تحديد الطول
        }

        // دالة إنشاء اسم الحزمة (نفس الدالة في المشروع)
        function generatePackageName(appName) {
            const cleanName = cleanAppName(appName);
            if (cleanName) {
                return `com.webtoapp.${cleanName}`;
            } else if (appName.trim()) {
                // إذا لم يكن هناك أحرف إنجليزية، استخدم اسم عام
                const timestamp = Date.now().toString().slice(-6);
                return `com.webtoapp.app${timestamp}`;
            } else {
                return '';
            }
        }

        // ربط الحدث
        document.getElementById('app_name').addEventListener('input', function() {
            const appName = this.value;
            const resultDiv = document.getElementById('result');
            const packageNameDiv = document.getElementById('package_name');
            
            if (appName.trim()) {
                const packageName = generatePackageName(appName);
                packageNameDiv.textContent = packageName;
                resultDiv.style.display = 'block';
                
                // تأثير بصري
                packageNameDiv.style.transition = 'color 0.3s';
                packageNameDiv.style.color = '#4caf50';
                setTimeout(() => {
                    packageNameDiv.style.color = '#2196F3';
                }, 300);
            } else {
                resultDiv.style.display = 'none';
            }
        });

        // أمثلة تفاعلية
        document.querySelectorAll('.examples li').forEach(li => {
            li.style.cursor = 'pointer';
            li.style.padding = '5px';
            li.style.borderRadius = '5px';
            li.style.transition = 'background 0.3s';
            
            li.addEventListener('mouseenter', function() {
                this.style.background = '#e3f2fd';
            });
            
            li.addEventListener('mouseleave', function() {
                this.style.background = 'transparent';
            });
            
            li.addEventListener('click', function() {
                document.getElementById('app_name').value = this.textContent;
                document.getElementById('app_name').dispatchEvent(new Event('input'));
            });
        });
    </script>
</body>
</html>
