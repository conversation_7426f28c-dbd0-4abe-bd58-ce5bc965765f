<?php
/**
 * معالج التثبيت التلقائي
 * Automatic Installation Handler
 */

$step = $_GET['step'] ?? 'welcome';
$action = $_POST['action'] ?? '';

// معالجة الإجراءات
if ($action === 'create_directories') {
    $results = [];
    $directories = [
        'temp' => __DIR__ . '/temp',
        'downloads' => __DIR__ . '/downloads', 
        'logs' => __DIR__ . '/logs'
    ];
    
    foreach ($directories as $name => $path) {
        if (!is_dir($path)) {
            if (mkdir($path, 0755, true)) {
                $results[$name] = "تم إنشاء مجلد $name بنجاح";
            } else {
                $results[$name] = "فشل في إنشاء مجلد $name";
            }
        } else {
            $results[$name] = "مجلد $name موجود بالفعل";
        }
    }
    
    // إنشاء ملف .htaccess للحماية
    $htaccess_content = "Order Deny,Allow\nDeny from all";
    file_put_contents(__DIR__ . '/temp/.htaccess', $htaccess_content);
    file_put_contents(__DIR__ . '/logs/.htaccess', $htaccess_content);
    
    $step = 'directories_created';
}

if ($action === 'test_system') {
    // إجراء اختبار سريع
    $test_results = [];
    
    // اختبار إنشاء ملف
    $test_file = __DIR__ . '/temp/test.txt';
    if (file_put_contents($test_file, 'test') !== false) {
        $test_results['file_creation'] = 'نجح';
        unlink($test_file);
    } else {
        $test_results['file_creation'] = 'فشل';
    }
    
    // اختبار مكتبة GD
    if (extension_loaded('gd')) {
        $test_image = imagecreatetruecolor(100, 100);
        if ($test_image) {
            $test_results['gd_library'] = 'نجح';
            imagedestroy($test_image);
        } else {
            $test_results['gd_library'] = 'فشل';
        }
    } else {
        $test_results['gd_library'] = 'غير متاح';
    }
    
    // اختبار مكتبة ZIP
    if (class_exists('ZipArchive')) {
        $zip = new ZipArchive();
        $test_zip = __DIR__ . '/temp/test.zip';
        if ($zip->open($test_zip, ZipArchive::CREATE) === TRUE) {
            $zip->addFromString('test.txt', 'test content');
            $zip->close();
            $test_results['zip_library'] = 'نجح';
            unlink($test_zip);
        } else {
            $test_results['zip_library'] = 'فشل';
        }
    } else {
        $test_results['zip_library'] = 'غير متاح';
    }
    
    $step = 'test_completed';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت محول الروابط إلى تطبيقات APK</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .install-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .install-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 20px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .step.active {
            background: #667eea;
        }
        
        .step.completed {
            background: #4caf50;
        }
        
        .install-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1rem;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .install-btn:hover {
            background: #5a6fd8;
        }
        
        .success-message {
            background: #e8f5e8;
            color: #4caf50;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
        
        .error-message {
            background: #ffebee;
            color: #f44336;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
        
        .results-list {
            text-align: right;
            margin: 20px 0;
        }
        
        .result-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <?php if ($step === 'welcome'): ?>
        <div class="install-card">
            <div class="step-indicator">
                <div class="step active">1</div>
                <div class="step">2</div>
                <div class="step">3</div>
                <div class="step">4</div>
            </div>
            
            <h1><i class="fas fa-rocket"></i> مرحباً بك!</h1>
            <p>مرحباً بك في معالج تثبيت محول الروابط إلى تطبيقات APK</p>
            <p>سيقوم هذا المعالج بإعداد المشروع وإنشاء المجلدات المطلوبة</p>
            
            <div style="margin: 30px 0;">
                <h3>ما سيتم فعله:</h3>
                <ul style="text-align: right; display: inline-block;">
                    <li>إنشاء المجلدات المطلوبة</li>
                    <li>إعداد ملفات الحماية</li>
                    <li>اختبار النظام</li>
                    <li>التحقق من المتطلبات</li>
                </ul>
            </div>
            
            <a href="?step=requirements" class="install-btn">
                <i class="fas fa-arrow-left"></i> التالي
            </a>
        </div>
        
        <?php elseif ($step === 'requirements'): ?>
        <div class="install-card">
            <div class="step-indicator">
                <div class="step completed">1</div>
                <div class="step active">2</div>
                <div class="step">3</div>
                <div class="step">4</div>
            </div>
            
            <h1><i class="fas fa-list-check"></i> فحص المتطلبات</h1>
            <p>التحقق من توفر المتطلبات الأساسية</p>
            
            <div class="results-list">
                <div class="result-item">
                    <span>إصدار PHP</span>
                    <span style="color: <?= version_compare(PHP_VERSION, '7.4.0', '>=') ? '#4caf50' : '#f44336' ?>">
                        <?= PHP_VERSION ?>
                    </span>
                </div>
                <div class="result-item">
                    <span>مكتبة GD</span>
                    <span style="color: <?= extension_loaded('gd') ? '#4caf50' : '#f44336' ?>">
                        <?= extension_loaded('gd') ? 'متاحة' : 'غير متاحة' ?>
                    </span>
                </div>
                <div class="result-item">
                    <span>مكتبة ZIP</span>
                    <span style="color: <?= extension_loaded('zip') ? '#4caf50' : '#f44336' ?>">
                        <?= extension_loaded('zip') ? 'متاحة' : 'غير متاحة' ?>
                    </span>
                </div>
                <div class="result-item">
                    <span>رفع الملفات</span>
                    <span style="color: <?= ini_get('file_uploads') ? '#4caf50' : '#f44336' ?>">
                        <?= ini_get('file_uploads') ? 'مفعل' : 'معطل' ?>
                    </span>
                </div>
            </div>
            
            <a href="?step=directories" class="install-btn">
                <i class="fas fa-arrow-left"></i> التالي
            </a>
        </div>
        
        <?php elseif ($step === 'directories'): ?>
        <div class="install-card">
            <div class="step-indicator">
                <div class="step completed">1</div>
                <div class="step completed">2</div>
                <div class="step active">3</div>
                <div class="step">4</div>
            </div>
            
            <h1><i class="fas fa-folder-plus"></i> إنشاء المجلدات</h1>
            <p>إنشاء المجلدات المطلوبة لتشغيل المشروع</p>
            
            <form method="post">
                <input type="hidden" name="action" value="create_directories">
                <button type="submit" class="install-btn">
                    <i class="fas fa-plus"></i> إنشاء المجلدات
                </button>
            </form>
        </div>
        
        <?php elseif ($step === 'directories_created'): ?>
        <div class="install-card">
            <div class="step-indicator">
                <div class="step completed">1</div>
                <div class="step completed">2</div>
                <div class="step completed">3</div>
                <div class="step active">4</div>
            </div>
            
            <h1><i class="fas fa-check-circle"></i> تم إنشاء المجلدات</h1>
            
            <div class="results-list">
                <?php foreach ($results as $name => $message): ?>
                <div class="result-item">
                    <span><?= htmlspecialchars($message) ?></span>
                    <i class="fas fa-check" style="color: #4caf50;"></i>
                </div>
                <?php endforeach; ?>
            </div>
            
            <a href="?step=testing" class="install-btn">
                <i class="fas fa-arrow-left"></i> التالي
            </a>
        </div>
        
        <?php elseif ($step === 'testing'): ?>
        <div class="install-card">
            <h1><i class="fas fa-vial"></i> اختبار النظام</h1>
            <p>إجراء اختبارات للتأكد من عمل النظام بشكل صحيح</p>
            
            <form method="post">
                <input type="hidden" name="action" value="test_system">
                <button type="submit" class="install-btn">
                    <i class="fas fa-play"></i> بدء الاختبار
                </button>
            </form>
        </div>
        
        <?php elseif ($step === 'test_completed'): ?>
        <div class="install-card">
            <h1><i class="fas fa-clipboard-check"></i> نتائج الاختبار</h1>
            
            <div class="results-list">
                <?php foreach ($test_results as $test => $result): ?>
                <div class="result-item">
                    <span>
                        <?php
                        switch($test) {
                            case 'file_creation': echo 'إنشاء الملفات'; break;
                            case 'gd_library': echo 'مكتبة GD'; break;
                            case 'zip_library': echo 'مكتبة ZIP'; break;
                        }
                        ?>
                    </span>
                    <span style="color: <?= $result === 'نجح' ? '#4caf50' : '#f44336' ?>">
                        <?= $result ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>
            
            <a href="?step=completed" class="install-btn">
                <i class="fas fa-arrow-left"></i> إنهاء التثبيت
            </a>
        </div>
        
        <?php elseif ($step === 'completed'): ?>
        <div class="install-card">
            <div style="font-size: 4rem; color: #4caf50; margin-bottom: 20px;">
                <i class="fas fa-check-circle"></i>
            </div>
            
            <h1>تم التثبيت بنجاح!</h1>
            <p>تم إعداد محول الروابط إلى تطبيقات APK بنجاح</p>
            
            <div class="success-message">
                <strong>المشروع جاهز للاستخدام!</strong><br>
                يمكنك الآن البدء في تحويل المواقع إلى تطبيقات APK
            </div>
            
            <div style="margin-top: 30px;">
                <a href="index.html" class="install-btn">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
                <a href="check.php" class="install-btn">
                    <i class="fas fa-stethoscope"></i> فحص النظام
                </a>
            </div>
            
            <div style="margin-top: 20px; font-size: 0.9rem; color: #666;">
                <p>نصائح للبدء:</p>
                <ul style="text-align: right; display: inline-block;">
                    <li>تأكد من تغيير كلمة مرور الإدارة</li>
                    <li>راجع ملف README.md للمزيد من المعلومات</li>
                    <li>استخدم صفحة الإحصائيات لمراقبة الأداء</li>
                </ul>
            </div>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
