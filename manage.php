<?php
require_once 'config.php';

// التحقق من كلمة المرور
$password = $_POST['pass'] ?? $_GET['pass'] ?? '';
if ($password !== 'admin123') {
    die('غير مصرح لك بالوصول');
}

// معالجة الإجراءات
$action = $_POST['action'] ?? '';
$message = '';

if ($action === 'delete_file') {
    $filename = $_POST['filename'] ?? '';
    $filepath = DOWNLOADS_DIR . '/' . basename($filename);
    
    if (file_exists($filepath) && unlink($filepath)) {
        $message = "تم حذف الملف: $filename";
        logActivity('FILE_DELETED', $filename);
    } else {
        $message = "فشل في حذف الملف: $filename";
    }
}

if ($action === 'cleanup_temp') {
    cleanupOldFiles();
    $message = "تم تنظيف الملفات المؤقتة";
    logActivity('CLEANUP_PERFORMED', 'تنظيف يدوي للملفات');
}

if ($action === 'clear_logs') {
    $log_files = ['activity.log', 'error.log', 'php_errors.log'];
    foreach ($log_files as $log_file) {
        $filepath = __DIR__ . '/logs/' . $log_file;
        if (file_exists($filepath)) {
            file_put_contents($filepath, '');
        }
    }
    $message = "تم مسح جميع السجلات";
}

// الحصول على قائمة الملفات
function getDownloadFiles() {
    $files = [];
    if (is_dir(DOWNLOADS_DIR)) {
        $scan = scandir(DOWNLOADS_DIR);
        foreach ($scan as $file) {
            if ($file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'apk') {
                $filepath = DOWNLOADS_DIR . '/' . $file;
                $files[] = [
                    'name' => $file,
                    'size' => filesize($filepath),
                    'date' => filemtime($filepath),
                    'path' => $filepath
                ];
            }
        }
    }
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    usort($files, function($a, $b) {
        return $b['date'] - $a['date'];
    });
    
    return $files;
}

function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

$files = getDownloadFiles();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الملفات</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .manage-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .actions-bar {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .action-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .action-btn:hover {
            background: #5a6fd8;
        }
        
        .action-btn.danger {
            background: #f44336;
        }
        
        .action-btn.danger:hover {
            background: #d32f2f;
        }
        
        .files-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f5f5f5;
            font-weight: bold;
        }
        
        .file-actions {
            display: flex;
            gap: 10px;
        }
        
        .file-actions button {
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .download-btn {
            background: #4caf50;
            color: white;
        }
        
        .delete-btn {
            background: #f44336;
            color: white;
        }
        
        .message {
            background: #4caf50;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="manage-container">
        <header>
            <h1><i class="fas fa-cog"></i> إدارة الملفات</h1>
            <p>إدارة ملفات APK والملفات المؤقتة</p>
        </header>

        <?php if ($message): ?>
        <div class="message">
            <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
        </div>
        <?php endif; ?>

        <div class="actions-bar">
            <h3>إجراءات سريعة</h3>
            <form method="post" style="display: inline;">
                <input type="hidden" name="pass" value="<?= htmlspecialchars($password) ?>">
                <button type="submit" name="action" value="cleanup_temp" class="action-btn">
                    <i class="fas fa-broom"></i> تنظيف الملفات المؤقتة
                </button>
                <button type="submit" name="action" value="clear_logs" class="action-btn danger" 
                        onclick="return confirm('هل أنت متأكد من مسح جميع السجلات؟')">
                    <i class="fas fa-trash"></i> مسح السجلات
                </button>
            </form>
            
            <a href="stats.php?pass=<?= urlencode($password) ?>" class="action-btn">
                <i class="fas fa-chart-bar"></i> عرض الإحصائيات
            </a>
            
            <a href="index.html" class="action-btn">
                <i class="fas fa-home"></i> الرئيسية
            </a>
        </div>

        <div class="files-table">
            <h3 style="padding: 20px; margin: 0; border-bottom: 1px solid #eee;">
                <i class="fas fa-file-archive"></i> ملفات APK المتاحة (<?= count($files) ?>)
            </h3>
            
            <?php if (empty($files)): ?>
            <div class="empty-state">
                <i class="fas fa-folder-open" style="font-size: 3rem; color: #ccc; margin-bottom: 20px;"></i>
                <p>لا توجد ملفات APK متاحة</p>
            </div>
            <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>اسم الملف</th>
                        <th>الحجم</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($files as $file): ?>
                    <tr>
                        <td>
                            <i class="fas fa-mobile-alt"></i>
                            <?= htmlspecialchars($file['name']) ?>
                        </td>
                        <td><?= formatBytes($file['size']) ?></td>
                        <td><?= date('Y-m-d H:i:s', $file['date']) ?></td>
                        <td>
                            <div class="file-actions">
                                <a href="downloads/<?= urlencode($file['name']) ?>" 
                                   class="download-btn" download>
                                    <i class="fas fa-download"></i> تحميل
                                </a>
                                
                                <form method="post" style="display: inline;" 
                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا الملف؟')">
                                    <input type="hidden" name="pass" value="<?= htmlspecialchars($password) ?>">
                                    <input type="hidden" name="action" value="delete_file">
                                    <input type="hidden" name="filename" value="<?= htmlspecialchars($file['name']) ?>">
                                    <button type="submit" class="delete-btn">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h3><i class="fas fa-info-circle"></i> معلومات التخزين</h3>
            <p><strong>مجلد التحميلات:</strong> <?= DOWNLOADS_DIR ?></p>
            <p><strong>مجلد الملفات المؤقتة:</strong> <?= TEMP_DIR ?></p>
            <p><strong>إجمالي المساحة المستخدمة:</strong> 
               <?= formatBytes(getFolderSize(DOWNLOADS_DIR) + getFolderSize(TEMP_DIR)) ?>
            </p>
        </div>
    </div>

    <script>
        // تحديث الصفحة كل 30 ثانية
        setTimeout(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
