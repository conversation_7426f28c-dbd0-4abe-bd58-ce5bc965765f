@echo off
chcp 65001 >nul
echo ========================================
echo    محول الروابط إلى تطبيقات APK
echo ========================================
echo.

echo جاري التحقق من PHP...
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP غير مثبت أو غير موجود في PATH
    echo.
    echo يرجى تثبيت PHP أولاً:
    echo 1. تحميل XAMPP من: https://www.apachefriends.org/
    echo 2. أو تحميل PHP من: https://www.php.net/downloads.php
    echo 3. إضافة PHP إلى متغير البيئة PATH
    echo.
    pause
    exit /b 1
)

echo ✅ PHP مثبت بنجاح
echo.

echo جاري التحقق من المكتبات المطلوبة...
php -m | findstr /i "gd" >nul
if %errorlevel% neq 0 (
    echo ⚠️  تحذير: مكتبة GD غير مثبتة - قد لا تعمل معالجة الصور
)

php -m | findstr /i "zip" >nul
if %errorlevel% neq 0 (
    echo ⚠️  تحذير: مكتبة ZIP غير مثبتة - قد لا يعمل إنشاء APK
)

echo.
echo جاري التحقق من التثبيت...
if not exist "temp" (
    echo ⚠️  المشروع غير مثبت - سيتم فتح معالج التثبيت
    start http://localhost:8000/install.php
    goto :start_server
)
if not exist "downloads" (
    echo ⚠️  المشروع غير مثبت - سيتم فتح معالج التثبيت
    start http://localhost:8000/install.php
    goto :start_server
)
echo ✅ المشروع مثبت ومجهز

:start_server

echo.
echo جاري تشغيل الخادم المحلي...
echo.
echo 🌐 الموقع متاح على: http://localhost:8000
echo 📱 لإيقاف الخادم اضغط Ctrl+C
echo.

start http://localhost:8000
php -S localhost:8000

pause
