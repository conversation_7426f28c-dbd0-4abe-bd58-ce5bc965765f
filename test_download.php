<?php
/**
 * ملف اختبار التحميل
 * Test Download File
 */

// إنشاء ملف تجريبي للاختبار
$test_content = "هذا ملف اختبار للتحقق من عمل التحميل\n";
$test_content .= "Test file for download functionality\n";
$test_content .= "تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n";
$test_content .= "Created: " . date('Y-m-d H:i:s') . "\n";

// إعداد headers للتحميل
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="test_download.txt"');
header('Content-Length: ' . strlen($test_content));
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');

// إرسال المحتوى
echo $test_content;
exit;
?>
