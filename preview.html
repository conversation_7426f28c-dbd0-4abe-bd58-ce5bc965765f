<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة التطبيق</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .preview-container {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .phone-frame {
            background: #333;
            border-radius: 25px;
            padding: 20px;
            position: relative;
        }
        
        .phone-screen {
            background: white;
            border-radius: 15px;
            height: 600px;
            position: relative;
            overflow: hidden;
        }
        
        .status-bar {
            background: #000;
            color: white;
            padding: 5px 15px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .app-header {
            background: var(--theme-color, #2196F3);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .app-content {
            height: calc(100% - 80px);
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .website-preview {
            width: 90%;
            height: 80%;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .controls {
            margin-top: 30px;
            text-align: center;
        }
        
        .control-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .control-btn:hover {
            background: #5a6fd8;
        }
        
        .app-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            background: #2196F3;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .loading-website {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-eye"></i> معاينة التطبيق</h1>
            <p>شاهد كيف سيبدو تطبيقك على الهاتف</p>
        </header>

        <div class="preview-container">
            <div class="phone-frame">
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>
                            <i class="fas fa-signal"></i>
                            <i class="fas fa-wifi"></i>
                            <i class="fas fa-battery-three-quarters"></i>
                        </span>
                    </div>
                    
                    <div class="app-header" id="app-header">
                        <span id="app-name">اسم التطبيق</span>
                    </div>
                    
                    <div class="app-content">
                        <div class="website-preview">
                            <div class="app-icon" id="app-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div id="website-url">https://example.com</div>
                            <div class="loading-website">جاري تحميل الموقع...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" onclick="rotatePhone()">
                <i class="fas fa-sync-alt"></i> تدوير الشاشة
            </button>
            <button class="control-btn" onclick="toggleFullscreen()">
                <i class="fas fa-expand"></i> ملء الشاشة
            </button>
            <button class="control-btn" onclick="goBack()">
                <i class="fas fa-arrow-right"></i> العودة
            </button>
        </div>
    </div>

    <script>
        // الحصول على البيانات من URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const appName = urlParams.get('app_name') || 'اسم التطبيق';
        const websiteUrl = urlParams.get('website_url') || 'https://example.com';
        const themeColor = urlParams.get('theme_color') || '#2196F3';
        const appIcon = urlParams.get('app_icon');

        // تطبيق البيانات
        document.getElementById('app-name').textContent = appName;
        document.getElementById('website-url').textContent = websiteUrl;
        document.getElementById('app-header').style.background = themeColor;
        document.documentElement.style.setProperty('--theme-color', themeColor);

        // تطبيق الأيقونة إذا كانت متاحة
        if (appIcon) {
            document.getElementById('app-icon').innerHTML = `<img src="${appIcon}" style="width: 100%; height: 100%; border-radius: 10px;">`;
        }

        let isRotated = false;
        let isFullscreen = false;

        function rotatePhone() {
            const phoneFrame = document.querySelector('.phone-frame');
            isRotated = !isRotated;
            
            if (isRotated) {
                phoneFrame.style.transform = 'rotate(90deg) scale(0.8)';
                phoneFrame.style.width = '600px';
                phoneFrame.style.height = '400px';
            } else {
                phoneFrame.style.transform = 'rotate(0deg) scale(1)';
                phoneFrame.style.width = 'auto';
                phoneFrame.style.height = 'auto';
            }
        }

        function toggleFullscreen() {
            const appHeader = document.getElementById('app-header');
            const statusBar = document.querySelector('.status-bar');
            
            isFullscreen = !isFullscreen;
            
            if (isFullscreen) {
                appHeader.style.display = 'none';
                statusBar.style.display = 'none';
            } else {
                appHeader.style.display = 'block';
                statusBar.style.display = 'flex';
            }
        }

        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = 'index.html';
            }
        }

        // محاكاة تحميل الموقع
        setTimeout(() => {
            document.querySelector('.loading-website').textContent = 'تم تحميل الموقع بنجاح';
            document.querySelector('.website-preview').style.background = 'linear-gradient(45deg, #f0f0f0, #ffffff)';
        }, 2000);
    </script>
</body>
</html>
