#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

echo "========================================"
echo "    محول الروابط إلى تطبيقات APK"
echo "========================================"
echo

echo "جاري التحقق من PHP..."
if ! command -v php &> /dev/null; then
    echo "❌ PHP غير مثبت"
    echo
    echo "يرجى تثبيت PHP أولاً:"
    echo "Ubuntu/Debian: sudo apt install php php-gd php-zip"
    echo "macOS: brew install php"
    echo "CentOS/RHEL: sudo yum install php php-gd php-zip"
    echo
    exit 1
fi

echo "✅ PHP مثبت بنجاح"
php --version | head -1
echo

echo "جاري التحقق من المكتبات المطلوبة..."

# التحقق من مكتبة GD
if ! php -m | grep -i gd &> /dev/null; then
    echo "⚠️  تحذير: مكتبة GD غير مثبتة - قد لا تعمل معالجة الصور"
    echo "   لتثبيتها: sudo apt install php-gd (Ubuntu/Debian)"
else
    echo "✅ مكتبة GD متاحة"
fi

# التحقق من مكتبة ZIP
if ! php -m | grep -i zip &> /dev/null; then
    echo "⚠️  تحذير: مكتبة ZIP غير مثبتة - قد لا يعمل إنشاء APK"
    echo "   لتثبيتها: sudo apt install php-zip (Ubuntu/Debian)"
else
    echo "✅ مكتبة ZIP متاحة"
fi

echo
echo "جاري إنشاء المجلدات المطلوبة..."
mkdir -p temp downloads
chmod 755 temp downloads
echo "✅ تم إنشاء المجلدات"

echo
echo "جاري تشغيل الخادم المحلي..."
echo
echo "🌐 الموقع متاح على: http://localhost:8000"
echo "📱 لإيقاف الخادم اضغط Ctrl+C"
echo

# فتح المتصفح (إذا كان متاحاً)
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:8000 &
elif command -v open &> /dev/null; then
    open http://localhost:8000 &
fi

# تشغيل الخادم
php -S localhost:8000
