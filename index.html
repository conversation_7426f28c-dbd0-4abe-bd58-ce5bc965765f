<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول الروابط إلى تطبيقات APK</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-mobile-alt"></i> محول الروابط إلى تطبيقات APK</h1>
            <p>حول أي موقع إلكتروني إلى تطبيق أندرويد بسهولة</p>
        </header>

        <div class="form-container">
            <form id="apkForm" enctype="multipart/form-data">
                <!-- معلومات أساسية -->
                <div class="section">
                    <h2><i class="fas fa-info-circle"></i> المعلومات الأساسية</h2>

                    <div class="info-box" style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-right: 4px solid #2196F3;">
                        <h4 style="margin: 0 0 10px 0; color: #1976d2;">
                            <i class="fas fa-lightbulb"></i> نصيحة سريعة
                        </h4>
                        <p style="margin: 0; font-size: 0.9rem; color: #1565c0;">
                            لا تحتاج لكتابة اسم الحزمة! سيتم إنشاؤه تلقائياً من اسم التطبيق.
                            <br>مثال: "متجر الكتب" → com.webtoapp.store
                        </p>
                    </div>
                    
                    <div class="input-group">
                        <label for="website_url">رابط الموقع الإلكتروني *</label>
                        <input type="url" id="website_url" name="website_url" required 
                               placeholder="https://example.com">
                    </div>

                    <div class="input-group">
                        <label for="app_name">اسم التطبيق *</label>
                        <input type="text" id="app_name" name="app_name" required
                               placeholder="مثال: متجري الإلكتروني">
                        <small style="display: block; margin-top: 5px; color: #666; font-size: 0.8rem;">
                            <i class="fas fa-lightbulb"></i>
                            سيتم إنشاء اسم الحزمة تلقائياً من اسم التطبيق
                        </small>
                    </div>

                    <div class="input-group">
                        <label for="package_name">
                            اسم الحزمة
                            <button type="button" id="edit_package_btn" class="edit-package-btn" onclick="togglePackageEdit()">
                                <i class="fas fa-edit"></i> تخصيص
                            </button>
                        </label>
                        <input type="text" id="package_name" name="package_name"
                               placeholder="com.webtoapp.myapp" readonly>
                        <small style="display: block; margin-top: 5px; color: #666; font-size: 0.8rem;">
                            <i class="fas fa-info-circle"></i>
                            يتم إنشاؤه تلقائياً - اضغط "تخصيص" للتعديل اليدوي
                        </small>
                    </div>
                </div>

                <!-- تخصيص الأيقونة -->
                <div class="section">
                    <h2><i class="fas fa-image"></i> أيقونة التطبيق</h2>
                    
                    <div class="icon-upload">
                        <input type="file" id="app_icon" name="app_icon" accept="image/*">
                        <label for="app_icon" class="upload-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>اختر أيقونة التطبيق</span>
                            <small>PNG, JPG (مستحسن: 512x512 بكسل)</small>
                        </label>
                        <div id="icon-preview"></div>
                    </div>
                </div>

                <!-- إعدادات التطبيق -->
                <div class="section">
                    <h2><i class="fas fa-cog"></i> إعدادات التطبيق</h2>
                    
                    <div class="input-group">
                        <label for="app_version">إصدار التطبيق</label>
                        <input type="text" id="app_version" name="app_version" 
                               value="1.0" placeholder="1.0">
                    </div>

                    <div class="input-group">
                        <label for="orientation">اتجاه الشاشة</label>
                        <select id="orientation" name="orientation">
                            <option value="portrait">عمودي</option>
                            <option value="landscape">أفقي</option>
                            <option value="sensor">تلقائي</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <label for="theme_color">لون الموضوع</label>
                        <input type="color" id="theme_color" name="theme_color" value="#2196F3">
                    </div>

                    <div class="input-group">
                        <label for="fullscreen">وضع ملء الشاشة</label>
                        <input type="checkbox" id="fullscreen" name="fullscreen">
                    </div>
                </div>

                <!-- إعدادات متقدمة -->
                <div class="section">
                    <h2><i class="fas fa-tools"></i> إعدادات متقدمة</h2>
                    
                    <div class="input-group">
                        <label for="user_agent">User Agent مخصص</label>
                        <input type="text" id="user_agent" name="user_agent" 
                               placeholder="اتركه فارغاً للاستخدام الافتراضي">
                    </div>

                    <div class="input-group">
                        <label for="splash_screen">شاشة البداية</label>
                        <input type="checkbox" id="splash_screen" name="splash_screen" checked>
                    </div>

                    <div class="input-group">
                        <label for="allow_zoom">السماح بالتكبير</label>
                        <input type="checkbox" id="allow_zoom" name="allow_zoom" checked>
                    </div>
                </div>

                <div class="button-group">
                    <button type="button" class="preview-btn" onclick="showPreview()">
                        <i class="fas fa-eye"></i>
                        معاينة التطبيق
                    </button>
                    <button type="submit" class="generate-btn">
                        <i class="fas fa-download"></i>
                        إنشاء تطبيق APK
                    </button>
                </div>
            </form>
        </div>

        <!-- منطقة النتائج -->
        <div id="result-section" class="result-section" style="display: none;">
            <h2><i class="fas fa-check-circle"></i> تم إنشاء التطبيق بنجاح!</h2>
            <div class="download-info">
                <p>تم إنشاء تطبيق APK بنجاح. يمكنك تحميله الآن:</p>
                <a id="download-link" href="#" class="download-btn">
                    <i class="fas fa-download"></i>
                    تحميل APK
                </a>
            </div>
        </div>

        <!-- منطقة التحميل -->
        <div id="loading-section" class="loading-section" style="display: none;">
            <div class="loader"></div>
            <p>جاري إنشاء التطبيق... يرجى الانتظار</p>
        </div>
    </div>

    <footer>
        <p>&copy; 2024 محول الروابط إلى تطبيقات APK. جميع الحقوق محفوظة.</p>
        <p style="margin-top: 10px; font-size: 0.8rem; opacity: 0.7;">
            <a href="check.php" style="color: white; text-decoration: none;">
                <i class="fas fa-stethoscope"></i> فحص النظام
            </a>
            |
            <a href="stats.php?pass=admin123" style="color: white; text-decoration: none;">
                <i class="fas fa-chart-bar"></i> إحصائيات
            </a>
            |
            <a href="manage.php?pass=admin123" style="color: white; text-decoration: none;">
                <i class="fas fa-cog"></i> إدارة
            </a>
            |
            <a href="update.php?pass=admin123" style="color: white; text-decoration: none;">
                <i class="fas fa-sync-alt"></i> تحديث
            </a>
        </p>
    </footer>

    <script src="script.js"></script>
</body>
</html>
